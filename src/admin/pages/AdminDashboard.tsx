import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { Card, Statistic, Row, Col, Button, Modal, Form, Input, Select, message, Layout } from 'antd';
import {
  BookOutlined,
  MessageOutlined,
  UserOutlined,
  PlusOutlined,
  TeamOutlined,
  MailOutlined,
  AreaChartOutlined
} from '@ant-design/icons';
import { useLanguage } from '../../context/LanguageContext';

const { Header } = Layout;

interface DashboardStats {
  totalScholarships: number;
  totalMessages: number;
  totalSubscribers: number;
  totalAdmins: number;
}

interface AdminInfo {
  id: number;
  email: string;
  name: string;
  role: string;
  isMainAdmin: boolean;
}

const AdminDashboard: React.FC = () => {
  const { translations } = useLanguage();
  const [stats, setStats] = useState<DashboardStats>({
    totalScholarships: 0,
    totalMessages: 0,
    totalSubscribers: 0,
    totalAdmins: 0
  });
  const [adminInfo, setAdminInfo] = useState<AdminInfo | null>(null);
  const [isAddAdminModalVisible, setIsAddAdminModalVisible] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    const fetchData = async () => {
      try {
        const api = axios.create({
          baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',
          headers: {
            'Content-Type': 'application/json',
          },
          withCredentials: true, // Use HTTP-only cookies for authentication
        });

        // Fetch stats from API
        const response = await api.get('/api/admin/stats');
        console.log('API stats response:', response.data);

        if (response.data) {
          // Use API data
          const dashboardStats: DashboardStats = {
            totalScholarships: response.data.totalScholarships || 0,
            totalMessages: response.data.totalMessages || 0,
            totalSubscribers: response.data.totalSubscribers || 0,
            totalAdmins: response.data.totalAdmins || 0
          };
          setStats(dashboardStats);
        } else {
          throw new Error('Invalid API response');
        }

        // Get admin info from API
        try {
          const adminResponse = await api.get('/api/admin/me');
          console.log('Admin info response:', adminResponse.data);

          if (adminResponse.data) {
            const adminData = adminResponse.data;
            const currentAdminInfo: AdminInfo = {
              id: adminData.id || 0,
              email: adminData.email || '',
              name: adminData.name || 'Admin',
              role: adminData.role || 'admin',
              isMainAdmin: adminData.isMainAdmin || false
            };
            setAdminInfo(currentAdminInfo);
          }
        } catch (adminError) {
          console.error('Error fetching admin info:', adminError);
          // If we can't fetch admin info, the user might not be authenticated
          // The SecureAuthContext will handle redirecting to login if needed
        }

        // Stats and admin info are already set in the API sections above
        console.log('Dashboard data fetched:', { stats, adminInfo });
      } catch (error) {
        console.error('Error fetching data:', error);
        message.error('Failed to fetch dashboard data');
      }
    };

    fetchData();
  }, []);

  const handleAddAdmin = async (values: any) => {
    try {
      // Check if current admin is the main admin
      if (!adminInfo?.isMainAdmin) {
        message.error('Only the main admin can create new admins');
        return;
      }

      console.log('Creating new admin with values:', values);

      // Check if we're trying to create a main admin
      if (values.email.toLowerCase() === '<EMAIL>') {
        message.error('Cannot create another Main Admin account');
        return;
      }

      const api = axios.create({
        baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',
        headers: {
          'Content-Type': 'application/json',
        },
        withCredentials: true, // Use HTTP-only cookies for authentication
      });

      // Create new admin via API
      const adminData = {
        name: values.name,
        email: values.email,
        role: values.role,
        password: values.password || 'DefaultPassword123!', // Default password if not provided
        isMainAdmin: false
      };

      // Send request to create admin
      const response = await api.post('/api/admin', adminData);
      console.log('Admin created response:', response.data);

      // Update stats with incremented admin count
      setStats(prevStats => ({
        ...prevStats,
        totalAdmins: prevStats.totalAdmins + 1
      }));

      message.success('Admin created successfully');
      setIsAddAdminModalVisible(false);
      form.resetFields();
    } catch (error: any) {
      if (error.response && error.response.status === 409) {
        message.error('An admin with this email already exists');
      } else {
        message.error('Failed to create admin');
        console.error('Error creating admin:', error);
      }
    }
  };

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Header className="bg-white shadow-sm fixed w-full top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-32">
            <div className="flex items-center">
              <Link to="/admin/dashboard" className="flex items-center space-x-4 group">
                <div className="relative">
                  <img
                    src="/assets/images/MaBoursedetudeLogo.jpeg"
                    alt={translations.brand.name}
                    className="h-20 w-auto rounded-lg shadow-lg transform transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div className="flex flex-col space-y-1">
                  <span className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight">
                    {translations.brand.name}
                  </span>
                  <span className="text-base text-gray-500 font-medium tracking-wider">
                    Admin Dashboard
                  </span>
                </div>
              </Link>
            </div>
            <div className="flex items-center">
              {/* Empty div to maintain the original layout spacing */}
            </div>
          </div>
        </div>
      </Header>

      <div className="pt-32 p-6">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 shadow-lg mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Admin Dashboard
              </h2>
              <p className="text-gray-600 mt-2">
                Welcome to your administration portal. Manage scholarships, messages, and more.
              </p>
              <div className="mt-4">
                <Link to="/admin/analytics" className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  <AreaChartOutlined className="mr-2" /> View Detailed Analytics
                </Link>
              </div>
            </div>
            {adminInfo?.isMainAdmin && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setIsAddAdminModalVisible(true)}
                size="large"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 border-none hover:from-blue-700 hover:to-indigo-700"
              >
                Add Admin
              </Button>
            )}
          </div>
        </div>

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card className="hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-white">
              <Statistic
                title="Total Scholarships"
                value={stats.totalScholarships}
                prefix={<BookOutlined className="text-blue-500 text-2xl" />}
                valueStyle={{ color: '#3f8600', fontSize: '24px' }}
              />
              <Link to="/admin/scholarships" className="text-blue-600 hover:text-blue-800 mt-4 block font-medium">
                View all scholarships →
              </Link>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className="hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-white">
              <Statistic
                title="Messages"
                value={stats.totalMessages}
                prefix={<MessageOutlined className="text-green-500 text-2xl" />}
                valueStyle={{ color: '#1890ff', fontSize: '24px' }}
              />
              <Link to="/admin/messages" className="text-green-600 hover:text-green-800 mt-4 block font-medium">
                View messages →
              </Link>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className="hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-white">
              <Statistic
                title="Newsletter Subscribers"
                value={stats.totalSubscribers}
                prefix={<MailOutlined className="text-purple-500 text-2xl" />}
                valueStyle={{ color: '#722ed1', fontSize: '24px' }}
              />
              <Link to="/admin/newsletter" className="text-purple-600 hover:text-purple-800 mt-4 block font-medium">
                Manage subscribers →
              </Link>
            </Card>
          </Col>
          {adminInfo?.isMainAdmin && (
            <Col xs={24} sm={12} lg={6}>
              <Card className="hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-white">
                <Statistic
                  title="Total Admins"
                  value={stats.totalAdmins}
                  prefix={<TeamOutlined className="text-cyan-500 text-2xl" />}
                  valueStyle={{ color: '#13c2c2', fontSize: '24px' }}
                />
                <Link to="/admin/admins" className="text-cyan-600 hover:text-cyan-800 mt-4 block font-medium">
                  Manage admins →
                </Link>
              </Card>
            </Col>
          )}
        </Row>

        <Modal
          title="Add New Admin"
          open={isAddAdminModalVisible}
          onCancel={() => setIsAddAdminModalVisible(false)}
          footer={null}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleAddAdmin}
          >
            <Form.Item
              name="name"
              label="Name"
              rules={[{ required: true, message: 'Please input admin name!' }]}
            >
              <Input prefix={<UserOutlined />} />
            </Form.Item>

            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please input admin email!' },
                { type: 'email', message: 'Please enter a valid email!' }
              ]}
            >
              <Input prefix={<MailOutlined />} />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: 'Please input admin password!' },
                { min: 8, message: 'Password must be at least 8 characters!' }
              ]}
            >
              <Input.Password />
            </Form.Item>

            <Form.Item
              name="role"
              label="Role"
              rules={[{ required: true, message: 'Please select admin role!' }]}
            >
              <Select>
                <Select.Option value="admin">Admin</Select.Option>
                <Select.Option value="super_admin">Super Admin</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" block>
                Create Admin
              </Button>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </Layout>
  );
};

export default AdminDashboard;