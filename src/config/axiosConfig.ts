import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { getEnv, getBoolEnv } from '../utils/envValidator';

// Create a custom axios instance with default configuration for HTTP-only cookie authentication
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',
  timeout: 15000, // 15 seconds
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Essential for HTTP-only cookies
});

// Request interceptor for adding additional headers (no token needed - using HTTP-only cookies)
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
    // Add debug headers if needed
    if (getBoolEnv('REACT_APP_DEBUG_MODE', false)) {
      config.headers = config.headers || {};
      config.headers['X-Debug-Mode'] = 'true';
    }

    return config;
  },
  (error: AxiosError): Promise<AxiosError> => {
    // Log request errors in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Request error:', error);
    }
    return Promise.reject(error);
  }
);

// Response interceptor for handling common response patterns and errors
apiClient.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    // Handle successful responses
    return response;
  },
  (error: AxiosError): Promise<AxiosError> => {
    // Handle 401 Unauthorized errors (authentication failed)
    if (error.response && error.response.status === 401) {
      // Redirect to admin login page if not already there
      if (!window.location.pathname.includes('/login')) {
        console.log('Authentication failed. Redirecting to login page...');
        window.location.href = '/admin/login';
      }
    }

    // Handle 403 Forbidden errors
    if (error.response && error.response.status === 403) {
      console.log('Access forbidden. You do not have permission to access this resource.');
    }

    // Handle 404 Not Found errors
    if (error.response && error.response.status === 404) {
      console.log('Resource not found.');
    }

    // Handle 500 Internal Server Error
    if (error.response && error.response.status >= 500) {
      console.error('Server error:', error.response.data);
    }

    // Log all errors in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Response error:', error);

      if (error.response) {
        console.error('Error data:', error.response.data);
        console.error('Error status:', error.response.status);
        console.error('Error headers:', error.response.headers);
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }
    }

    return Promise.reject(error);
  }
);

// Helper functions for common API operations
export const api = {
  /**
   * Make a GET request
   * @param url - The URL to request
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get<T>(url, config);
    return response.data;
  },

  /**
   * Make a POST request
   * @param url - The URL to request
   * @param data - The data to send
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post<T>(url, data, config);
    return response.data;
  },

  /**
   * Make a PUT request
   * @param url - The URL to request
   * @param data - The data to send
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put<T>(url, data, config);
    return response.data;
  },

  /**
   * Make a DELETE request
   * @param url - The URL to request
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete<T>(url, config);
    return response.data;
  },

  /**
   * Upload a file with multipart/form-data
   * @param url - The URL to request
   * @param formData - The FormData object with file and other data
   * @param config - Optional axios config
   * @returns Promise with the response data
   */
  upload: async <T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post<T>(url, formData, {
      ...config,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

export default apiClient;
