{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Form, Input, Button, Card, Alert, Typography, Spin } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst AdminLogin = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    login,\n    isAuthenticated,\n    isLoading,\n    error,\n    clearError\n  } = useAuth();\n  const [form] = Form.useForm();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated && !isLoading) {\n      navigate('/admin/dashboard', {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, isLoading, navigate]);\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n\n  // Handle form submission\n  const handleSubmit = async values => {\n    try {\n      setIsSubmitting(true);\n      await login(values.email, values.password);\n      // Navigation will be handled by the useEffect above\n    } catch (error) {\n      // Error is handled by the auth context\n      console.error('Login failed:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\",\n        tip: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          className: \"text-gray-900\",\n          children: \"MaBourse Admin Portal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          className: \"text-gray-600\",\n          children: \"Sign in to access the admin dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          name: \"admin-login\",\n          onFinish: handleSubmit,\n          layout: \"vertical\",\n          size: \"large\",\n          autoComplete: \"off\",\n          children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"Login Failed\",\n            description: error,\n            type: \"error\",\n            showIcon: true,\n            closable: true,\n            onClose: clearError,\n            className: \"mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"Email Address\",\n            rules: [{\n              required: true,\n              message: 'Please enter your email address'\n            }, {\n              type: 'email',\n              message: 'Please enter a valid email address'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n                className: \"text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 25\n              }, this),\n              placeholder: \"<EMAIL>\",\n              autoComplete: \"email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            rules: [{\n              required: true,\n              message: 'Please enter your password'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                className: \"text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 25\n              }, this),\n              placeholder: \"Enter your password\",\n              autoComplete: \"current-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: isSubmitting,\n              disabled: isSubmitting,\n              className: \"w-full h-12 text-lg font-medium\",\n              children: isSubmitting ? 'Signing In...' : 'Sign In'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-4\",\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            className: \"text-gray-500 text-sm\",\n            children: \"Secure authentication with HTTP-only cookies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          className: \"text-gray-400 text-xs\",\n          children: \"\\xA9 2025 MaBourse. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminLogin, \"PdP7G0oF6vbSD43Rt0k2vMoUFQ8=\", false, function () {\n  return [useNavigate, useAuth, Form.useForm];\n});\n_c = AdminLogin;\nexport default AdminLogin;\nvar _c;\n$RefreshReg$(_c, \"AdminLogin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Form", "Input", "<PERSON><PERSON>", "Card", "<PERSON><PERSON>", "Typography", "Spin", "UserOutlined", "LockOutlined", "useAuth", "jsxDEV", "_jsxDEV", "Title", "Text", "AdminLogin", "_s", "navigate", "login", "isAuthenticated", "isLoading", "error", "clearError", "form", "useForm", "isSubmitting", "setIsSubmitting", "replace", "handleSubmit", "values", "email", "password", "console", "className", "children", "size", "tip", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "name", "onFinish", "layout", "autoComplete", "message", "description", "type", "showIcon", "closable", "onClose", "<PERSON><PERSON>", "label", "rules", "required", "prefix", "placeholder", "Password", "htmlType", "loading", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Form, Input, Button, Card, Alert, Typography, Spin } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport DebugAuth from '../../components/DebugAuth';\n\nconst { Title, Text } = Typography;\n\ninterface LoginFormData {\n  email: string;\n  password: string;\n}\n\nconst AdminLogin: React.FC = () => {\n  const navigate = useNavigate();\n  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();\n  const [form] = Form.useForm();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated && !isLoading) {\n      navigate('/admin/dashboard', { replace: true });\n    }\n  }, [isAuthenticated, isLoading, navigate]);\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n\n  // Handle form submission\n  const handleSubmit = async (values: LoginFormData) => {\n    try {\n      setIsSubmitting(true);\n      await login(values.email, values.password);\n      // Navigation will be handled by the useEffect above\n    } catch (error) {\n      // Error is handled by the auth context\n      console.error('Login failed:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <Spin size=\"large\" tip=\"Loading...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <Title level={2} className=\"text-gray-900\">\n            MaBourse Admin Portal\n          </Title>\n          <Text className=\"text-gray-600\">\n            Sign in to access the admin dashboard\n          </Text>\n        </div>\n\n        <Card className=\"shadow-lg\">\n          <Form\n            form={form}\n            name=\"admin-login\"\n            onFinish={handleSubmit}\n            layout=\"vertical\"\n            size=\"large\"\n            autoComplete=\"off\"\n          >\n            {error && (\n              <Alert\n                message=\"Login Failed\"\n                description={error}\n                type=\"error\"\n                showIcon\n                closable\n                onClose={clearError}\n                className=\"mb-4\"\n              />\n            )}\n\n            <Form.Item\n              name=\"email\"\n              label=\"Email Address\"\n              rules={[\n                { required: true, message: 'Please enter your email address' },\n                { type: 'email', message: 'Please enter a valid email address' }\n              ]}\n            >\n              <Input\n                prefix={<UserOutlined className=\"text-gray-400\" />}\n                placeholder=\"<EMAIL>\"\n                autoComplete=\"email\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              label=\"Password\"\n              rules={[\n                { required: true, message: 'Please enter your password' }\n              ]}\n            >\n              <Input.Password\n                prefix={<LockOutlined className=\"text-gray-400\" />}\n                placeholder=\"Enter your password\"\n                autoComplete=\"current-password\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={isSubmitting}\n                disabled={isSubmitting}\n                className=\"w-full h-12 text-lg font-medium\"\n              >\n                {isSubmitting ? 'Signing In...' : 'Sign In'}\n              </Button>\n            </Form.Item>\n          </Form>\n\n          <div className=\"text-center mt-4\">\n            <Text className=\"text-gray-500 text-sm\">\n              Secure authentication with HTTP-only cookies\n            </Text>\n          </div>\n        </Card>\n\n        <div className=\"text-center\">\n          <Text className=\"text-gray-400 text-xs\">\n            © 2025 MaBourse. All rights reserved.\n          </Text>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,IAAI,QAAQ,MAAM;AACzE,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGrD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGR,UAAU;AAOlC,MAAMS,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB,KAAK;IAAEC,eAAe;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC1E,MAAM,CAACa,IAAI,CAAC,GAAGtB,IAAI,CAACuB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIoB,eAAe,IAAI,CAACC,SAAS,EAAE;MACjCH,QAAQ,CAAC,kBAAkB,EAAE;QAAEU,OAAO,EAAE;MAAK,CAAC,CAAC;IACjD;EACF,CAAC,EAAE,CAACR,eAAe,EAAEC,SAAS,EAAEH,QAAQ,CAAC,CAAC;;EAE1C;EACAlB,SAAS,CAAC,MAAM;IACduB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMM,YAAY,GAAG,MAAOC,MAAqB,IAAK;IACpD,IAAI;MACFH,eAAe,CAAC,IAAI,CAAC;MACrB,MAAMR,KAAK,CAACW,MAAM,CAACC,KAAK,EAAED,MAAM,CAACE,QAAQ,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd;MACAW,OAAO,CAACX,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRK,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,IAAIN,SAAS,EAAE;IACb,oBACER,OAAA;MAAKqB,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEtB,OAAA,CAACL,IAAI;QAAC4B,IAAI,EAAC,OAAO;QAACC,GAAG,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAEV;EAEA,oBACE5B,OAAA;IAAKqB,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAClGtB,OAAA;MAAKqB,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxCtB,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtB,OAAA,CAACC,KAAK;UAAC4B,KAAK,EAAE,CAAE;UAACR,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE3C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR5B,OAAA,CAACE,IAAI;UAACmB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAEhC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN5B,OAAA,CAACR,IAAI;QAAC6B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACzBtB,OAAA,CAACX,IAAI;UACHsB,IAAI,EAAEA,IAAK;UACXmB,IAAI,EAAC,aAAa;UAClBC,QAAQ,EAAEf,YAAa;UACvBgB,MAAM,EAAC,UAAU;UACjBT,IAAI,EAAC,OAAO;UACZU,YAAY,EAAC,KAAK;UAAAX,QAAA,GAEjBb,KAAK,iBACJT,OAAA,CAACP,KAAK;YACJyC,OAAO,EAAC,cAAc;YACtBC,WAAW,EAAE1B,KAAM;YACnB2B,IAAI,EAAC,OAAO;YACZC,QAAQ;YACRC,QAAQ;YACRC,OAAO,EAAE7B,UAAW;YACpBW,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACF,eAED5B,OAAA,CAACX,IAAI,CAACmD,IAAI;YACRV,IAAI,EAAC,OAAO;YACZW,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAET,OAAO,EAAE;YAAkC,CAAC,EAC9D;cAAEE,IAAI,EAAE,OAAO;cAAEF,OAAO,EAAE;YAAqC,CAAC,CAChE;YAAAZ,QAAA,eAEFtB,OAAA,CAACV,KAAK;cACJsD,MAAM,eAAE5C,OAAA,CAACJ,YAAY;gBAACyB,SAAS,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnDiB,WAAW,EAAC,oBAAoB;cAChCZ,YAAY,EAAC;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ5B,OAAA,CAACX,IAAI,CAACmD,IAAI;YACRV,IAAI,EAAC,UAAU;YACfW,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAET,OAAO,EAAE;YAA6B,CAAC,CACzD;YAAAZ,QAAA,eAEFtB,OAAA,CAACV,KAAK,CAACwD,QAAQ;cACbF,MAAM,eAAE5C,OAAA,CAACH,YAAY;gBAACwB,SAAS,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnDiB,WAAW,EAAC,qBAAqB;cACjCZ,YAAY,EAAC;YAAkB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ5B,OAAA,CAACX,IAAI,CAACmD,IAAI;YAAAlB,QAAA,eACRtB,OAAA,CAACT,MAAM;cACL6C,IAAI,EAAC,SAAS;cACdW,QAAQ,EAAC,QAAQ;cACjBC,OAAO,EAAEnC,YAAa;cACtBoC,QAAQ,EAAEpC,YAAa;cACvBQ,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAE1CT,YAAY,GAAG,eAAe,GAAG;YAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEP5B,OAAA;UAAKqB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BtB,OAAA,CAACE,IAAI;YAACmB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAExC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEP5B,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BtB,OAAA,CAACE,IAAI;UAACmB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAExC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CAnIID,UAAoB;EAAA,QACPf,WAAW,EACqCU,OAAO,EACzDT,IAAI,CAACuB,OAAO;AAAA;AAAAsC,EAAA,GAHvB/C,UAAoB;AAqI1B,eAAeA,UAAU;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}