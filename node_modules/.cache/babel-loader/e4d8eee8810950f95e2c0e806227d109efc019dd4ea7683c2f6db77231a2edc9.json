{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Card, Typography, Alert } from 'antd';\nimport { LockOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  Step\n} = Steps;\nvar RecoveryStep = /*#__PURE__*/function (RecoveryStep) {\n  RecoveryStep[RecoveryStep[\"REQUEST\"] = 0] = \"REQUEST\";\n  RecoveryStep[RecoveryStep[\"VERIFY\"] = 1] = \"VERIFY\";\n  RecoveryStep[RecoveryStep[\"RESET\"] = 2] = \"RESET\";\n  RecoveryStep[RecoveryStep[\"COMPLETE\"] = 3] = \"COMPLETE\";\n  return RecoveryStep;\n}(RecoveryStep || {});\nconst AccountRecovery = () => {\n  _s();\n  const {\n    token,\n    accountType\n  } = useParams();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [currentStep, setCurrentStep] = useState(token ? RecoveryStep.VERIFY : RecoveryStep.REQUEST);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [accountInfo, setAccountInfo] = useState(null);\n  const [authStatus, setAuthStatus] = useState(AuthStatus.IDLE);\n\n  // Verify token on component mount if token is provided\n  useEffect(() => {\n    if (token && accountType) {\n      verifyToken();\n    }\n  }, [token, accountType]);\n\n  // Verify recovery token\n  const verifyToken = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n      const response = await secureApiService.get(`/account-recovery/${accountType}/${token}/verify`);\n      if (response.success && response.data) {\n        setAccountInfo(response.data);\n        setCurrentStep(RecoveryStep.RESET);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Invalid or expired recovery token');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message || 'Failed to verify recovery token');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Request account recovery\n  const handleRequestRecovery = async values => {\n    try {\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n      const response = await secureApiService.post('/account-recovery/request', values);\n      if (response.success) {\n        setCurrentStep(RecoveryStep.VERIFY);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Failed to request account recovery');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || err.message || 'Failed to request account recovery');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset password\n  const handleResetPassword = async values => {\n    try {\n      if (values.password !== values.confirmPassword) {\n        setError('Passwords do not match');\n        return;\n      }\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n      const response = await secureApiService.post(`/account-recovery/${accountType}/${token}/reset-password`, {\n        password: values.password\n      });\n      if (response.success) {\n        setCurrentStep(RecoveryStep.COMPLETE);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Failed to reset password');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || err.message || 'Failed to reset password');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Render request recovery form\n  const renderRequestForm = () => /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    name: \"recoveryRequest\",\n    onFinish: handleRequestRecovery,\n    layout: \"vertical\",\n    initialValues: {\n      accountType: 'user'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n      name: \"email\",\n      label: \"Email Address\",\n      rules: [{\n        required: true,\n        message: 'Please enter your email address'\n      }, {\n        type: 'email',\n        message: 'Please enter a valid email address'\n      }],\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 24\n        }, this),\n        placeholder: \"Email\",\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      name: \"accountType\",\n      label: \"Account Type\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: form.getFieldValue('accountType') === 'user' ? 'primary' : 'default',\n          onClick: () => form.setFieldsValue({\n            accountType: 'user'\n          }),\n          className: \"flex-1\",\n          children: \"User Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: form.getFieldValue('accountType') === 'admin' ? 'primary' : 'default',\n          onClick: () => form.setFieldsValue({\n            accountType: 'admin'\n          }),\n          className: \"flex-1\",\n          children: \"Admin Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"submit\",\n        size: \"large\",\n        block: true,\n        loading: loading,\n        className: \"mt-4\",\n        children: \"Request Recovery\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n\n  // Render verify token step\n  const renderVerifyStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Check Your Email\",\n      description: \"We've sent recovery instructions to your email address. Please check your inbox and follow the instructions.\",\n      type: \"info\",\n      showIcon: true,\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n      children: /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"If you don't receive an email within a few minutes, check your spam folder or request a new recovery link.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      onClick: () => setCurrentStep(RecoveryStep.REQUEST),\n      className: \"mt-4\",\n      children: \"Request New Link\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n\n  // Render reset password form\n  const renderResetForm = () => /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    name: \"resetPassword\",\n    onFinish: handleResetPassword,\n    layout: \"vertical\",\n    children: [accountInfo && /*#__PURE__*/_jsxDEV(Alert, {\n      message: `Hello, ${accountInfo.name}`,\n      description: \"You can now reset your password. Please choose a strong password that you haven't used before.\",\n      type: \"success\",\n      showIcon: true,\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      name: \"password\",\n      label: \"New Password\",\n      rules: [{\n        required: true,\n        message: 'Please enter a new password'\n      }, {\n        min: 8,\n        message: 'Password must be at least 8 characters long'\n      }, {\n        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/,\n        message: 'Password must include uppercase, lowercase, number, and special character'\n      }],\n      children: /*#__PURE__*/_jsxDEV(Input.Password, {\n        prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 33\n        }, this),\n        placeholder: \"New Password\",\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      name: \"confirmPassword\",\n      label: \"Confirm Password\",\n      dependencies: ['password'],\n      rules: [{\n        required: true,\n        message: 'Please confirm your password'\n      }, ({\n        getFieldValue\n      }) => ({\n        validator(_, value) {\n          if (!value || getFieldValue('password') === value) {\n            return Promise.resolve();\n          }\n          return Promise.reject(new Error('The two passwords do not match'));\n        }\n      })],\n      children: /*#__PURE__*/_jsxDEV(Input.Password, {\n        prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 33\n        }, this),\n        placeholder: \"Confirm Password\",\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"submit\",\n        size: \"large\",\n        block: true,\n        loading: loading,\n        className: \"mt-4\",\n        children: \"Reset Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n\n  // Render completion step\n  const renderCompleteStep = () => /*#__PURE__*/_jsxDEV(Result, {\n    status: \"success\",\n    title: \"Password Reset Successful\",\n    subTitle: \"Your password has been reset successfully. You can now log in with your new password.\",\n    extra: [/*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      onClick: () => navigate(accountType === 'admin' ? '/admin/secure-login' : '/login'),\n      children: \"Go to Login\"\n    }, \"login\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 9\n    }, this)]\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 269,\n    columnNumber: 5\n  }, this);\n\n  // Render current step content\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case RecoveryStep.REQUEST:\n        return renderRequestForm();\n      case RecoveryStep.VERIFY:\n        return renderVerifyStep();\n      case RecoveryStep.RESET:\n        return renderResetForm();\n      case RecoveryStep.COMPLETE:\n        return renderCompleteStep();\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-100 p-4\",\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      className: \"w-full max-w-md shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 3,\n          children: \"Account Recovery\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"Recover your account access securely\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Steps, {\n        current: currentStep,\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(Step, {\n          title: \"Request\",\n          icon: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Step, {\n          title: \"Verify\",\n          icon: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Step, {\n          title: \"Reset\",\n          icon: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 37\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Step, {\n          title: \"Complete\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), authStatus === AuthStatus.LOADING && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.LOADING,\n        title: \"Processing\",\n        message: \"Please wait while we process your request...\",\n        showProgress: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this), authStatus === AuthStatus.SUCCESS && currentStep !== RecoveryStep.COMPLETE && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.SUCCESS,\n        title: \"Success\",\n        message: currentStep === RecoveryStep.VERIFY ? \"Recovery instructions sent to your email\" : \"Verification successful\",\n        autoHideDuration: 3000\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this), authStatus === AuthStatus.ERROR && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.ERROR,\n        title: \"Error\",\n        message: error || \"An error occurred\",\n        details: \"Please try again or contact support if the problem persists.\",\n        onClose: () => {\n          setError(null);\n          setAuthStatus(AuthStatus.IDLE);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), renderStepContent()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 302,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountRecovery, \"M1bqBxxDYcAVYgbIlZ8x/2QUKEk=\", true);\n_c = AccountRecovery;\nexport default AccountRecovery;\nvar _c;\n$RefreshReg$(_c, \"AccountRecovery\");", "map": {"version": 3, "names": ["React", "Card", "Typography", "<PERSON><PERSON>", "LockOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "Step", "Steps", "RecoveryStep", "Account<PERSON><PERSON><PERSON><PERSON>", "_s", "token", "accountType", "useParams", "navigate", "useNavigate", "form", "Form", "useForm", "currentStep", "setCurrentStep", "useState", "VERIFY", "REQUEST", "loading", "setLoading", "error", "setError", "accountInfo", "setAccountInfo", "authStatus", "setAuthStatus", "AuthStatus", "IDLE", "useEffect", "verifyToken", "LOADING", "response", "secureApiService", "get", "success", "data", "RESET", "SUCCESS", "message", "ERROR", "err", "_err$response", "_err$response$data", "handleRequestRecovery", "values", "post", "_err$response2", "_err$response2$data", "handleResetPassword", "password", "confirmPassword", "COMPLETE", "_err$response3", "_err$response3$data", "renderRequestForm", "name", "onFinish", "layout", "initialValues", "children", "<PERSON><PERSON>", "label", "rules", "required", "type", "Input", "prefix", "MailOutlined", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "size", "className", "<PERSON><PERSON>", "getFieldValue", "onClick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "htmlType", "block", "renderVerifyStep", "description", "showIcon", "renderResetForm", "min", "pattern", "Password", "dependencies", "validator", "_", "value", "Promise", "resolve", "reject", "Error", "renderCompleteStep", "Result", "status", "title", "subTitle", "extra", "renderStepContent", "level", "current", "icon", "SafetyOutlined", "CheckCircleOutlined", "AuthFeedback", "showProgress", "autoHideDuration", "details", "onClose", "Divider", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, Typography, Alert } from 'antd';\nimport { LockOutlined } from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Step } = Steps;\n\nenum RecoveryStep {\n  REQUEST = 0,\n  VERIFY = 1,\n  RESET = 2,\n  COMPLETE = 3,\n}\n\ninterface AccountInfo {\n  email: string;\n  name: string;\n  hasTwoFactor: boolean;\n}\n\nconst AccountRecovery: React.FC = () => {\n  const { token, accountType } = useParams<{ token?: string; accountType?: string }>();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n\n  const [currentStep, setCurrentStep] = useState<RecoveryStep>(\n    token ? RecoveryStep.VERIFY : RecoveryStep.REQUEST\n  );\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [accountInfo, setAccountInfo] = useState<AccountInfo | null>(null);\n  const [authStatus, setAuthStatus] = useState<AuthStatus>(AuthStatus.IDLE);\n\n  // Verify token on component mount if token is provided\n  useEffect(() => {\n    if (token && accountType) {\n      verifyToken();\n    }\n  }, [token, accountType]);\n\n  // Verify recovery token\n  const verifyToken = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n\n      const response = await secureApiService.get<ApiResponse<AccountInfo>>(`/account-recovery/${accountType}/${token}/verify`);\n\n      if (response.success && response.data) {\n        setAccountInfo(response.data);\n        setCurrentStep(RecoveryStep.RESET);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Invalid or expired recovery token');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'Failed to verify recovery token');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Request account recovery\n  const handleRequestRecovery = async (values: { email: string; accountType: string }) => {\n    try {\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n\n      const response = await secureApiService.post<ApiResponse>('/account-recovery/request', values);\n\n      if (response.success) {\n        setCurrentStep(RecoveryStep.VERIFY);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Failed to request account recovery');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'Failed to request account recovery');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset password\n  const handleResetPassword = async (values: { password: string; confirmPassword: string }) => {\n    try {\n      if (values.password !== values.confirmPassword) {\n        setError('Passwords do not match');\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n\n      const response = await secureApiService.post<ApiResponse>(\n        `/account-recovery/${accountType}/${token}/reset-password`,\n        { password: values.password }\n      );\n\n      if (response.success) {\n        setCurrentStep(RecoveryStep.COMPLETE);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Failed to reset password');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'Failed to reset password');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Render request recovery form\n  const renderRequestForm = () => (\n    <Form\n      form={form}\n      name=\"recoveryRequest\"\n      onFinish={handleRequestRecovery}\n      layout=\"vertical\"\n      initialValues={{ accountType: 'user' }}\n    >\n      <Form.Item\n        name=\"email\"\n        label=\"Email Address\"\n        rules={[\n          { required: true, message: 'Please enter your email address' },\n          { type: 'email', message: 'Please enter a valid email address' },\n        ]}\n      >\n        <Input prefix={<MailOutlined />} placeholder=\"Email\" size=\"large\" />\n      </Form.Item>\n\n      <Form.Item name=\"accountType\" label=\"Account Type\">\n        <div className=\"flex space-x-4\">\n          <Button\n            type={form.getFieldValue('accountType') === 'user' ? 'primary' : 'default'}\n            onClick={() => form.setFieldsValue({ accountType: 'user' })}\n            className=\"flex-1\"\n          >\n            User Account\n          </Button>\n          <Button\n            type={form.getFieldValue('accountType') === 'admin' ? 'primary' : 'default'}\n            onClick={() => form.setFieldsValue({ accountType: 'admin' })}\n            className=\"flex-1\"\n          >\n            Admin Account\n          </Button>\n        </div>\n      </Form.Item>\n\n      <Form.Item>\n        <Button\n          type=\"primary\"\n          htmlType=\"submit\"\n          size=\"large\"\n          block\n          loading={loading}\n          className=\"mt-4\"\n        >\n          Request Recovery\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n\n  // Render verify token step\n  const renderVerifyStep = () => (\n    <div className=\"text-center\">\n      <Alert\n        message=\"Check Your Email\"\n        description=\"We've sent recovery instructions to your email address. Please check your inbox and follow the instructions.\"\n        type=\"info\"\n        showIcon\n        className=\"mb-6\"\n      />\n      <Paragraph>\n        <Text type=\"secondary\">\n          If you don't receive an email within a few minutes, check your spam folder or request a new recovery link.\n        </Text>\n      </Paragraph>\n      <Button\n        onClick={() => setCurrentStep(RecoveryStep.REQUEST)}\n        className=\"mt-4\"\n      >\n        Request New Link\n      </Button>\n    </div>\n  );\n\n  // Render reset password form\n  const renderResetForm = () => (\n    <Form\n      form={form}\n      name=\"resetPassword\"\n      onFinish={handleResetPassword}\n      layout=\"vertical\"\n    >\n      {accountInfo && (\n        <Alert\n          message={`Hello, ${accountInfo.name}`}\n          description=\"You can now reset your password. Please choose a strong password that you haven't used before.\"\n          type=\"success\"\n          showIcon\n          className=\"mb-6\"\n        />\n      )}\n\n      <Form.Item\n        name=\"password\"\n        label=\"New Password\"\n        rules={[\n          { required: true, message: 'Please enter a new password' },\n          { min: 8, message: 'Password must be at least 8 characters long' },\n          {\n            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/,\n            message: 'Password must include uppercase, lowercase, number, and special character',\n          },\n        ]}\n      >\n        <Input.Password prefix={<LockOutlined />} placeholder=\"New Password\" size=\"large\" />\n      </Form.Item>\n\n      <Form.Item\n        name=\"confirmPassword\"\n        label=\"Confirm Password\"\n        dependencies={['password']}\n        rules={[\n          { required: true, message: 'Please confirm your password' },\n          ({ getFieldValue }) => ({\n            validator(_, value) {\n              if (!value || getFieldValue('password') === value) {\n                return Promise.resolve();\n              }\n              return Promise.reject(new Error('The two passwords do not match'));\n            },\n          }),\n        ]}\n      >\n        <Input.Password prefix={<LockOutlined />} placeholder=\"Confirm Password\" size=\"large\" />\n      </Form.Item>\n\n      <Form.Item>\n        <Button\n          type=\"primary\"\n          htmlType=\"submit\"\n          size=\"large\"\n          block\n          loading={loading}\n          className=\"mt-4\"\n        >\n          Reset Password\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n\n  // Render completion step\n  const renderCompleteStep = () => (\n    <Result\n      status=\"success\"\n      title=\"Password Reset Successful\"\n      subTitle=\"Your password has been reset successfully. You can now log in with your new password.\"\n      extra={[\n        <Button\n          type=\"primary\"\n          key=\"login\"\n          onClick={() => navigate(accountType === 'admin' ? '/admin/secure-login' : '/login')}\n        >\n          Go to Login\n        </Button>,\n      ]}\n    />\n  );\n\n  // Render current step content\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case RecoveryStep.REQUEST:\n        return renderRequestForm();\n      case RecoveryStep.VERIFY:\n        return renderVerifyStep();\n      case RecoveryStep.RESET:\n        return renderResetForm();\n      case RecoveryStep.COMPLETE:\n        return renderCompleteStep();\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-100 p-4\">\n      <Card className=\"w-full max-w-md shadow-lg\">\n        <div className=\"text-center mb-6\">\n          <Title level={3}>Account Recovery</Title>\n          <Text type=\"secondary\">\n            Recover your account access securely\n          </Text>\n        </div>\n\n        <Steps current={currentStep} className=\"mb-8\">\n          <Step title=\"Request\" icon={<MailOutlined />} />\n          <Step title=\"Verify\" icon={<SafetyOutlined />} />\n          <Step title=\"Reset\" icon={<LockOutlined />} />\n          <Step title=\"Complete\" icon={<CheckCircleOutlined />} />\n        </Steps>\n\n        {/* Authentication feedback */}\n        {authStatus === AuthStatus.LOADING && (\n          <AuthFeedback\n            status={AuthStatus.LOADING}\n            title=\"Processing\"\n            message=\"Please wait while we process your request...\"\n            showProgress={false}\n          />\n        )}\n\n        {authStatus === AuthStatus.SUCCESS && currentStep !== RecoveryStep.COMPLETE && (\n          <AuthFeedback\n            status={AuthStatus.SUCCESS}\n            title=\"Success\"\n            message={\n              currentStep === RecoveryStep.VERIFY\n                ? \"Recovery instructions sent to your email\"\n                : \"Verification successful\"\n            }\n            autoHideDuration={3000}\n          />\n        )}\n\n        {authStatus === AuthStatus.ERROR && (\n          <AuthFeedback\n            status={AuthStatus.ERROR}\n            title=\"Error\"\n            message={error || \"An error occurred\"}\n            details=\"Please try again or contact support if the problem persists.\"\n            onClose={() => {\n              setError(null);\n              setAuthStatus(AuthStatus.IDLE);\n            }}\n          />\n        )}\n\n        <Divider />\n\n        {renderStepContent()}\n      </Card>\n    </div>\n  );\n};\n\nexport default AccountRecovery;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,UAAU,EAAEC,KAAK,QAAQ,MAAM;AAC9C,SAASC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGP,UAAU;AAC7C,MAAM;EAAEQ;AAAK,CAAC,GAAGC,KAAK;AAAC,IAElBC,YAAY,0BAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAA,OAAZA,YAAY;AAAA,EAAZA,YAAY;AAajB,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,KAAK;IAAEC;EAAY,CAAC,GAAGC,SAAS,CAA2C,CAAC;EACpF,MAAMC,QAAQ,GAAGC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACC,IAAI,CAAC,GAAGC,IAAI,CAACC,OAAO,CAAC,CAAC;EAE7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGC,QAAQ,CAC5CV,KAAK,GAAGH,YAAY,CAACc,MAAM,GAAGd,YAAY,CAACe,OAC7C,CAAC;EACD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGJ,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACK,KAAK,EAAEC,QAAQ,CAAC,GAAGN,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAaW,UAAU,CAACC,IAAI,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd,IAAIvB,KAAK,IAAIC,WAAW,EAAE;MACxBuB,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACxB,KAAK,EAAEC,WAAW,CAAC,CAAC;;EAExB;EACA,MAAMuB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdI,aAAa,CAACC,UAAU,CAACI,OAAO,CAAC;MAEjC,MAAMC,QAAQ,GAAG,MAAMC,gBAAgB,CAACC,GAAG,CAA2B,qBAAqB3B,WAAW,IAAID,KAAK,SAAS,CAAC;MAEzH,IAAI0B,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;QACrCZ,cAAc,CAACQ,QAAQ,CAACI,IAAI,CAAC;QAC7BrB,cAAc,CAACZ,YAAY,CAACkC,KAAK,CAAC;QAClCX,aAAa,CAACC,UAAU,CAACW,OAAO,CAAC;MACnC,CAAC,MAAM;QACLhB,QAAQ,CAACU,QAAQ,CAACO,OAAO,IAAI,mCAAmC,CAAC;QACjEb,aAAa,CAACC,UAAU,CAACa,KAAK,CAAC;MACjC;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBrB,QAAQ,CAAC,EAAAoB,aAAA,GAAAD,GAAG,CAACT,QAAQ,cAAAU,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcN,IAAI,cAAAO,kBAAA,uBAAlBA,kBAAA,CAAoBJ,OAAO,KAAIE,GAAG,CAACF,OAAO,IAAI,iCAAiC,CAAC;MACzFb,aAAa,CAACC,UAAU,CAACa,KAAK,CAAC;IACjC,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwB,qBAAqB,GAAG,MAAOC,MAA8C,IAAK;IACtF,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdI,aAAa,CAACC,UAAU,CAACI,OAAO,CAAC;MAEjC,MAAMC,QAAQ,GAAG,MAAMC,gBAAgB,CAACa,IAAI,CAAc,2BAA2B,EAAED,MAAM,CAAC;MAE9F,IAAIb,QAAQ,CAACG,OAAO,EAAE;QACpBpB,cAAc,CAACZ,YAAY,CAACc,MAAM,CAAC;QACnCS,aAAa,CAACC,UAAU,CAACW,OAAO,CAAC;MACnC,CAAC,MAAM;QACLhB,QAAQ,CAACU,QAAQ,CAACO,OAAO,IAAI,oCAAoC,CAAC;QAClEb,aAAa,CAACC,UAAU,CAACa,KAAK,CAAC;MACjC;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAM,cAAA,EAAAC,mBAAA;MACjB1B,QAAQ,CAAC,EAAAyB,cAAA,GAAAN,GAAG,CAACT,QAAQ,cAAAe,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcX,IAAI,cAAAY,mBAAA,uBAAlBA,mBAAA,CAAoBT,OAAO,KAAIE,GAAG,CAACF,OAAO,IAAI,oCAAoC,CAAC;MAC5Fb,aAAa,CAACC,UAAU,CAACa,KAAK,CAAC;IACjC,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6B,mBAAmB,GAAG,MAAOJ,MAAqD,IAAK;IAC3F,IAAI;MACF,IAAIA,MAAM,CAACK,QAAQ,KAAKL,MAAM,CAACM,eAAe,EAAE;QAC9C7B,QAAQ,CAAC,wBAAwB,CAAC;QAClC;MACF;MAEAF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdI,aAAa,CAACC,UAAU,CAACI,OAAO,CAAC;MAEjC,MAAMC,QAAQ,GAAG,MAAMC,gBAAgB,CAACa,IAAI,CAC1C,qBAAqBvC,WAAW,IAAID,KAAK,iBAAiB,EAC1D;QAAE4C,QAAQ,EAAEL,MAAM,CAACK;MAAS,CAC9B,CAAC;MAED,IAAIlB,QAAQ,CAACG,OAAO,EAAE;QACpBpB,cAAc,CAACZ,YAAY,CAACiD,QAAQ,CAAC;QACrC1B,aAAa,CAACC,UAAU,CAACW,OAAO,CAAC;MACnC,CAAC,MAAM;QACLhB,QAAQ,CAACU,QAAQ,CAACO,OAAO,IAAI,0BAA0B,CAAC;QACxDb,aAAa,CAACC,UAAU,CAACa,KAAK,CAAC;MACjC;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAY,cAAA,EAAAC,mBAAA;MACjBhC,QAAQ,CAAC,EAAA+B,cAAA,GAAAZ,GAAG,CAACT,QAAQ,cAAAqB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjB,IAAI,cAAAkB,mBAAA,uBAAlBA,mBAAA,CAAoBf,OAAO,KAAIE,GAAG,CAACF,OAAO,IAAI,0BAA0B,CAAC;MAClFb,aAAa,CAACC,UAAU,CAACa,KAAK,CAAC;IACjC,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,iBAAiB,GAAGA,CAAA,kBACxB1D,OAAA,CAACe,IAAI;IACHD,IAAI,EAAEA,IAAK;IACX6C,IAAI,EAAC,iBAAiB;IACtBC,QAAQ,EAAEb,qBAAsB;IAChCc,MAAM,EAAC,UAAU;IACjBC,aAAa,EAAE;MAAEpD,WAAW,EAAE;IAAO,CAAE;IAAAqD,QAAA,gBAEvC/D,OAAA,CAACe,IAAI,CAACiD,IAAI;MACRL,IAAI,EAAC,OAAO;MACZM,KAAK,EAAC,eAAe;MACrBC,KAAK,EAAE,CACL;QAAEC,QAAQ,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAAkC,CAAC,EAC9D;QAAE0B,IAAI,EAAE,OAAO;QAAE1B,OAAO,EAAE;MAAqC,CAAC,CAChE;MAAAqB,QAAA,eAEF/D,OAAA,CAACqE,KAAK;QAACC,MAAM,eAAEtE,OAAA,CAACuE,YAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACC,WAAW,EAAC,OAAO;QAACC,IAAI,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAEZ3E,OAAA,CAACe,IAAI,CAACiD,IAAI;MAACL,IAAI,EAAC,aAAa;MAACM,KAAK,EAAC,cAAc;MAAAF,QAAA,eAChD/D,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAf,QAAA,gBAC7B/D,OAAA,CAAC+E,MAAM;UACLX,IAAI,EAAEtD,IAAI,CAACkE,aAAa,CAAC,aAAa,CAAC,KAAK,MAAM,GAAG,SAAS,GAAG,SAAU;UAC3EC,OAAO,EAAEA,CAAA,KAAMnE,IAAI,CAACoE,cAAc,CAAC;YAAExE,WAAW,EAAE;UAAO,CAAC,CAAE;UAC5DoE,SAAS,EAAC,QAAQ;UAAAf,QAAA,EACnB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3E,OAAA,CAAC+E,MAAM;UACLX,IAAI,EAAEtD,IAAI,CAACkE,aAAa,CAAC,aAAa,CAAC,KAAK,OAAO,GAAG,SAAS,GAAG,SAAU;UAC5EC,OAAO,EAAEA,CAAA,KAAMnE,IAAI,CAACoE,cAAc,CAAC;YAAExE,WAAW,EAAE;UAAQ,CAAC,CAAE;UAC7DoE,SAAS,EAAC,QAAQ;UAAAf,QAAA,EACnB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEZ3E,OAAA,CAACe,IAAI,CAACiD,IAAI;MAAAD,QAAA,eACR/D,OAAA,CAAC+E,MAAM;QACLX,IAAI,EAAC,SAAS;QACde,QAAQ,EAAC,QAAQ;QACjBN,IAAI,EAAC,OAAO;QACZO,KAAK;QACL9D,OAAO,EAAEA,OAAQ;QACjBwD,SAAS,EAAC,MAAM;QAAAf,QAAA,EACjB;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACP;;EAED;EACA,MAAMU,gBAAgB,GAAGA,CAAA,kBACvBrF,OAAA;IAAK8E,SAAS,EAAC,aAAa;IAAAf,QAAA,gBAC1B/D,OAAA,CAACH,KAAK;MACJ6C,OAAO,EAAC,kBAAkB;MAC1B4C,WAAW,EAAC,8GAA8G;MAC1HlB,IAAI,EAAC,MAAM;MACXmB,QAAQ;MACRT,SAAS,EAAC;IAAM;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eACF3E,OAAA,CAACG,SAAS;MAAA4D,QAAA,eACR/D,OAAA,CAACE,IAAI;QAACkE,IAAI,EAAC,WAAW;QAAAL,QAAA,EAAC;MAEvB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACZ3E,OAAA,CAAC+E,MAAM;MACLE,OAAO,EAAEA,CAAA,KAAM/D,cAAc,CAACZ,YAAY,CAACe,OAAO,CAAE;MACpDyD,SAAS,EAAC,MAAM;MAAAf,QAAA,EACjB;IAED;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;;EAED;EACA,MAAMa,eAAe,GAAGA,CAAA,kBACtBxF,OAAA,CAACe,IAAI;IACHD,IAAI,EAAEA,IAAK;IACX6C,IAAI,EAAC,eAAe;IACpBC,QAAQ,EAAER,mBAAoB;IAC9BS,MAAM,EAAC,UAAU;IAAAE,QAAA,GAEhBrC,WAAW,iBACV1B,OAAA,CAACH,KAAK;MACJ6C,OAAO,EAAE,UAAUhB,WAAW,CAACiC,IAAI,EAAG;MACtC2B,WAAW,EAAC,gGAAgG;MAC5GlB,IAAI,EAAC,SAAS;MACdmB,QAAQ;MACRT,SAAS,EAAC;IAAM;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACF,eAED3E,OAAA,CAACe,IAAI,CAACiD,IAAI;MACRL,IAAI,EAAC,UAAU;MACfM,KAAK,EAAC,cAAc;MACpBC,KAAK,EAAE,CACL;QAAEC,QAAQ,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAA8B,CAAC,EAC1D;QAAE+C,GAAG,EAAE,CAAC;QAAE/C,OAAO,EAAE;MAA8C,CAAC,EAClE;QACEgD,OAAO,EAAE,sEAAsE;QAC/EhD,OAAO,EAAE;MACX,CAAC,CACD;MAAAqB,QAAA,eAEF/D,OAAA,CAACqE,KAAK,CAACsB,QAAQ;QAACrB,MAAM,eAAEtE,OAAA,CAACF,YAAY;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACC,WAAW,EAAC,cAAc;QAACC,IAAI,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CAAC,eAEZ3E,OAAA,CAACe,IAAI,CAACiD,IAAI;MACRL,IAAI,EAAC,iBAAiB;MACtBM,KAAK,EAAC,kBAAkB;MACxB2B,YAAY,EAAE,CAAC,UAAU,CAAE;MAC3B1B,KAAK,EAAE,CACL;QAAEC,QAAQ,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAA+B,CAAC,EAC3D,CAAC;QAAEsC;MAAc,CAAC,MAAM;QACtBa,SAASA,CAACC,CAAC,EAAEC,KAAK,EAAE;UAClB,IAAI,CAACA,KAAK,IAAIf,aAAa,CAAC,UAAU,CAAC,KAAKe,KAAK,EAAE;YACjD,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;UAC1B;UACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpE;MACF,CAAC,CAAC,CACF;MAAApC,QAAA,eAEF/D,OAAA,CAACqE,KAAK,CAACsB,QAAQ;QAACrB,MAAM,eAAEtE,OAAA,CAACF,YAAY;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACC,WAAW,EAAC,kBAAkB;QAACC,IAAI,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,eAEZ3E,OAAA,CAACe,IAAI,CAACiD,IAAI;MAAAD,QAAA,eACR/D,OAAA,CAAC+E,MAAM;QACLX,IAAI,EAAC,SAAS;QACde,QAAQ,EAAC,QAAQ;QACjBN,IAAI,EAAC,OAAO;QACZO,KAAK;QACL9D,OAAO,EAAEA,OAAQ;QACjBwD,SAAS,EAAC,MAAM;QAAAf,QAAA,EACjB;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACP;;EAED;EACA,MAAMyB,kBAAkB,GAAGA,CAAA,kBACzBpG,OAAA,CAACqG,MAAM;IACLC,MAAM,EAAC,SAAS;IAChBC,KAAK,EAAC,2BAA2B;IACjCC,QAAQ,EAAC,uFAAuF;IAChGC,KAAK,EAAE,cACLzG,OAAA,CAAC+E,MAAM;MACLX,IAAI,EAAC,SAAS;MAEda,OAAO,EAAEA,CAAA,KAAMrE,QAAQ,CAACF,WAAW,KAAK,OAAO,GAAG,qBAAqB,GAAG,QAAQ,CAAE;MAAAqD,QAAA,EACrF;IAED,GAJM,OAAO;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIL,CAAC;EACT;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;;EAED;EACA,MAAM+B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQzF,WAAW;MACjB,KAAKX,YAAY,CAACe,OAAO;QACvB,OAAOqC,iBAAiB,CAAC,CAAC;MAC5B,KAAKpD,YAAY,CAACc,MAAM;QACtB,OAAOiE,gBAAgB,CAAC,CAAC;MAC3B,KAAK/E,YAAY,CAACkC,KAAK;QACrB,OAAOgD,eAAe,CAAC,CAAC;MAC1B,KAAKlF,YAAY,CAACiD,QAAQ;QACxB,OAAO6C,kBAAkB,CAAC,CAAC;MAC7B;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEpG,OAAA;IAAK8E,SAAS,EAAC,+DAA+D;IAAAf,QAAA,eAC5E/D,OAAA,CAACL,IAAI;MAACmF,SAAS,EAAC,2BAA2B;MAAAf,QAAA,gBACzC/D,OAAA;QAAK8E,SAAS,EAAC,kBAAkB;QAAAf,QAAA,gBAC/B/D,OAAA,CAACC,KAAK;UAAC0G,KAAK,EAAE,CAAE;UAAA5C,QAAA,EAAC;QAAgB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzC3E,OAAA,CAACE,IAAI;UAACkE,IAAI,EAAC,WAAW;UAAAL,QAAA,EAAC;QAEvB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN3E,OAAA,CAACK,KAAK;QAACuG,OAAO,EAAE3F,WAAY;QAAC6D,SAAS,EAAC,MAAM;QAAAf,QAAA,gBAC3C/D,OAAA,CAACI,IAAI;UAACmG,KAAK,EAAC,SAAS;UAACM,IAAI,eAAE7G,OAAA,CAACuE,YAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChD3E,OAAA,CAACI,IAAI;UAACmG,KAAK,EAAC,QAAQ;UAACM,IAAI,eAAE7G,OAAA,CAAC8G,cAAc;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjD3E,OAAA,CAACI,IAAI;UAACmG,KAAK,EAAC,OAAO;UAACM,IAAI,eAAE7G,OAAA,CAACF,YAAY;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C3E,OAAA,CAACI,IAAI;UAACmG,KAAK,EAAC,UAAU;UAACM,IAAI,eAAE7G,OAAA,CAAC+G,mBAAmB;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,EAGP/C,UAAU,KAAKE,UAAU,CAACI,OAAO,iBAChClC,OAAA,CAACgH,YAAY;QACXV,MAAM,EAAExE,UAAU,CAACI,OAAQ;QAC3BqE,KAAK,EAAC,YAAY;QAClB7D,OAAO,EAAC,8CAA8C;QACtDuE,YAAY,EAAE;MAAM;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACF,EAEA/C,UAAU,KAAKE,UAAU,CAACW,OAAO,IAAIxB,WAAW,KAAKX,YAAY,CAACiD,QAAQ,iBACzEvD,OAAA,CAACgH,YAAY;QACXV,MAAM,EAAExE,UAAU,CAACW,OAAQ;QAC3B8D,KAAK,EAAC,SAAS;QACf7D,OAAO,EACLzB,WAAW,KAAKX,YAAY,CAACc,MAAM,GAC/B,0CAA0C,GAC1C,yBACL;QACD8F,gBAAgB,EAAE;MAAK;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACF,EAEA/C,UAAU,KAAKE,UAAU,CAACa,KAAK,iBAC9B3C,OAAA,CAACgH,YAAY;QACXV,MAAM,EAAExE,UAAU,CAACa,KAAM;QACzB4D,KAAK,EAAC,OAAO;QACb7D,OAAO,EAAElB,KAAK,IAAI,mBAAoB;QACtC2F,OAAO,EAAC,8DAA8D;QACtEC,OAAO,EAAEA,CAAA,KAAM;UACb3F,QAAQ,CAAC,IAAI,CAAC;UACdI,aAAa,CAACC,UAAU,CAACC,IAAI,CAAC;QAChC;MAAE;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,eAED3E,OAAA,CAACqH,OAAO;QAAA7C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEV+B,iBAAiB,CAAC,CAAC;IAAA;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACnE,EAAA,CAnVID,eAAyB;AAAA+G,EAAA,GAAzB/G,eAAyB;AAqV/B,eAAeA,eAAe;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}