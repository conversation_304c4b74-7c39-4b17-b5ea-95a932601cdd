{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { Spin } from 'antd';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n/**\n * Protected route component for admin authentication\n */\nconst ProtectedRoute = ({\n  children,\n  requireMainAdmin = false\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading,\n    admin\n  } = useAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\",\n        tip: \"Verifying authentication...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check main admin requirement\n  if (requireMainAdmin && !(admin !== null && admin !== void 0 && admin.isMainAdmin)) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/admin/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Render protected content\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(ProtectedRoute, \"U3qNNj7qRccnN1m6DNKt3tRuQok=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "Spin", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "require<PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "isAuthenticated", "isLoading", "admin", "className", "size", "tip", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { Spin } from 'antd';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requireMainAdmin?: boolean;\n}\n\n/**\n * Protected route component for admin authentication\n */\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ \n  children, \n  requireMainAdmin = false \n}) => {\n  const { isAuthenticated, isLoading, admin } = useAuth();\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Spin size=\"large\" tip=\"Verifying authentication...\" />\n      </div>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/admin/login\" replace />;\n  }\n\n  // Check main admin requirement\n  if (requireMainAdmin && !admin?.isMainAdmin) {\n    return <Navigate to=\"/admin/dashboard\" replace />;\n  }\n\n  // Render protected content\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOlD;AACA;AACA;AACA,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,QAAQ;EACRC,gBAAgB,GAAG;AACrB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,eAAe;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAGX,OAAO,CAAC,CAAC;;EAEvD;EACA,IAAIU,SAAS,EAAE;IACb,oBACER,OAAA;MAAKU,SAAS,EAAC,+CAA+C;MAAAN,QAAA,eAC5DJ,OAAA,CAACH,IAAI;QAACc,IAAI,EAAC,OAAO;QAACC,GAAG,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAEV;;EAEA;EACA,IAAI,CAACT,eAAe,EAAE;IACpB,oBAAOP,OAAA,CAACJ,QAAQ;MAACqB,EAAE,EAAC,cAAc;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/C;;EAEA;EACA,IAAIX,gBAAgB,IAAI,EAACI,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEU,WAAW,GAAE;IAC3C,oBAAOnB,OAAA,CAACJ,QAAQ;MAACqB,EAAE,EAAC,kBAAkB;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnD;;EAEA;EACA,oBAAOhB,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACE,EAAA,CA3BIH,cAA6C;EAAA,QAIHL,OAAO;AAAA;AAAAsB,EAAA,GAJjDjB,cAA6C;AA6BnD,eAAeA,cAAc;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}