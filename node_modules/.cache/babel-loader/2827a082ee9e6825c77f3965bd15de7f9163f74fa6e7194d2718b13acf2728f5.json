{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Form, Input, Button, Card, Steps, Typography, Alert, Divider, Result } from 'antd';\nimport { LockOutlined, MailOutlined, SafetyOutlined, CheckCircleOutlined } from '@ant-design/icons';\n\n// AuthFeedback component removed - using Ant Design components for feedback\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  Step\n} = Steps;\nvar RecoveryStep = /*#__PURE__*/function (RecoveryStep) {\n  RecoveryStep[RecoveryStep[\"REQUEST\"] = 0] = \"REQUEST\";\n  RecoveryStep[RecoveryStep[\"VERIFY\"] = 1] = \"VERIFY\";\n  RecoveryStep[RecoveryStep[\"RESET\"] = 2] = \"RESET\";\n  RecoveryStep[RecoveryStep[\"COMPLETE\"] = 3] = \"COMPLETE\";\n  return RecoveryStep;\n}(RecoveryStep || {});\nconst AccountRecovery = () => {\n  _s();\n  const {\n    token,\n    accountType\n  } = useParams();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [currentStep, setCurrentStep] = useState(token ? RecoveryStep.VERIFY : RecoveryStep.REQUEST);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [accountInfo, setAccountInfo] = useState(null);\n  const [authStatus, setAuthStatus] = useState(AuthStatus.IDLE);\n\n  // Verify token on component mount if token is provided\n  useEffect(() => {\n    if (token && accountType) {\n      verifyToken();\n    }\n  }, [token, accountType]);\n\n  // Verify recovery token\n  const verifyToken = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n      const response = await secureApiService.get(`/account-recovery/${accountType}/${token}/verify`);\n      if (response.success && response.data) {\n        setAccountInfo(response.data);\n        setCurrentStep(RecoveryStep.RESET);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Invalid or expired recovery token');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message || 'Failed to verify recovery token');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Request account recovery\n  const handleRequestRecovery = async values => {\n    try {\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n      const response = await secureApiService.post('/account-recovery/request', values);\n      if (response.success) {\n        setCurrentStep(RecoveryStep.VERIFY);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Failed to request account recovery');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || err.message || 'Failed to request account recovery');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset password\n  const handleResetPassword = async values => {\n    try {\n      if (values.password !== values.confirmPassword) {\n        setError('Passwords do not match');\n        return;\n      }\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n      const response = await secureApiService.post(`/account-recovery/${accountType}/${token}/reset-password`, {\n        password: values.password\n      });\n      if (response.success) {\n        setCurrentStep(RecoveryStep.COMPLETE);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Failed to reset password');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || err.message || 'Failed to reset password');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Render request recovery form\n  const renderRequestForm = () => /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    name: \"recoveryRequest\",\n    onFinish: handleRequestRecovery,\n    layout: \"vertical\",\n    initialValues: {\n      accountType: 'user'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n      name: \"email\",\n      label: \"Email Address\",\n      rules: [{\n        required: true,\n        message: 'Please enter your email address'\n      }, {\n        type: 'email',\n        message: 'Please enter a valid email address'\n      }],\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 24\n        }, this),\n        placeholder: \"Email\",\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      name: \"accountType\",\n      label: \"Account Type\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: form.getFieldValue('accountType') === 'user' ? 'primary' : 'default',\n          onClick: () => form.setFieldsValue({\n            accountType: 'user'\n          }),\n          className: \"flex-1\",\n          children: \"User Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: form.getFieldValue('accountType') === 'admin' ? 'primary' : 'default',\n          onClick: () => form.setFieldsValue({\n            accountType: 'admin'\n          }),\n          className: \"flex-1\",\n          children: \"Admin Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"submit\",\n        size: \"large\",\n        block: true,\n        loading: loading,\n        className: \"mt-4\",\n        children: \"Request Recovery\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n\n  // Render verify token step\n  const renderVerifyStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Check Your Email\",\n      description: \"We've sent recovery instructions to your email address. Please check your inbox and follow the instructions.\",\n      type: \"info\",\n      showIcon: true,\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n      children: /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"If you don't receive an email within a few minutes, check your spam folder or request a new recovery link.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      onClick: () => setCurrentStep(RecoveryStep.REQUEST),\n      className: \"mt-4\",\n      children: \"Request New Link\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n\n  // Render reset password form\n  const renderResetForm = () => /*#__PURE__*/_jsxDEV(Form, {\n    form: form,\n    name: \"resetPassword\",\n    onFinish: handleResetPassword,\n    layout: \"vertical\",\n    children: [accountInfo && /*#__PURE__*/_jsxDEV(Alert, {\n      message: `Hello, ${accountInfo.name}`,\n      description: \"You can now reset your password. Please choose a strong password that you haven't used before.\",\n      type: \"success\",\n      showIcon: true,\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      name: \"password\",\n      label: \"New Password\",\n      rules: [{\n        required: true,\n        message: 'Please enter a new password'\n      }, {\n        min: 8,\n        message: 'Password must be at least 8 characters long'\n      }, {\n        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/,\n        message: 'Password must include uppercase, lowercase, number, and special character'\n      }],\n      children: /*#__PURE__*/_jsxDEV(Input.Password, {\n        prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 33\n        }, this),\n        placeholder: \"New Password\",\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      name: \"confirmPassword\",\n      label: \"Confirm Password\",\n      dependencies: ['password'],\n      rules: [{\n        required: true,\n        message: 'Please confirm your password'\n      }, ({\n        getFieldValue\n      }) => ({\n        validator(_, value) {\n          if (!value || getFieldValue('password') === value) {\n            return Promise.resolve();\n          }\n          return Promise.reject(new Error('The two passwords do not match'));\n        }\n      })],\n      children: /*#__PURE__*/_jsxDEV(Input.Password, {\n        prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 33\n        }, this),\n        placeholder: \"Confirm Password\",\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        htmlType: \"submit\",\n        size: \"large\",\n        block: true,\n        loading: loading,\n        className: \"mt-4\",\n        children: \"Reset Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 5\n  }, this);\n\n  // Render completion step\n  const renderCompleteStep = () => /*#__PURE__*/_jsxDEV(Result, {\n    status: \"success\",\n    title: \"Password Reset Successful\",\n    subTitle: \"Your password has been reset successfully. You can now log in with your new password.\",\n    extra: [/*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      onClick: () => navigate(accountType === 'admin' ? '/admin/secure-login' : '/login'),\n      children: \"Go to Login\"\n    }, \"login\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 9\n    }, this)]\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 5\n  }, this);\n\n  // Render current step content\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case RecoveryStep.REQUEST:\n        return renderRequestForm();\n      case RecoveryStep.VERIFY:\n        return renderVerifyStep();\n      case RecoveryStep.RESET:\n        return renderResetForm();\n      case RecoveryStep.COMPLETE:\n        return renderCompleteStep();\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-100 p-4\",\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      className: \"w-full max-w-md shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 3,\n          children: \"Account Recovery\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"Recover your account access securely\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Steps, {\n        current: currentStep,\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(Step, {\n          title: \"Request\",\n          icon: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 39\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Step, {\n          title: \"Verify\",\n          icon: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Step, {\n          title: \"Reset\",\n          icon: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 37\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Step, {\n          title: \"Complete\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), authStatus === AuthStatus.LOADING && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.LOADING,\n        title: \"Processing\",\n        message: \"Please wait while we process your request...\",\n        showProgress: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this), authStatus === AuthStatus.SUCCESS && currentStep !== RecoveryStep.COMPLETE && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.SUCCESS,\n        title: \"Success\",\n        message: currentStep === RecoveryStep.VERIFY ? \"Recovery instructions sent to your email\" : \"Verification successful\",\n        autoHideDuration: 3000\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this), authStatus === AuthStatus.ERROR && /*#__PURE__*/_jsxDEV(AuthFeedback, {\n        status: AuthStatus.ERROR,\n        title: \"Error\",\n        message: error || \"An error occurred\",\n        details: \"Please try again or contact support if the problem persists.\",\n        onClose: () => {\n          setError(null);\n          setAuthStatus(AuthStatus.IDLE);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), renderStepContent()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountRecovery, \"M1bqBxxDYcAVYgbIlZ8x/2QUKEk=\", false, function () {\n  return [useParams, useNavigate, Form.useForm];\n});\n_c = AccountRecovery;\nexport default AccountRecovery;\nvar _c;\n$RefreshReg$(_c, \"AccountRecovery\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Form", "Input", "<PERSON><PERSON>", "Card", "Steps", "Typography", "<PERSON><PERSON>", "Divider", "Result", "LockOutlined", "MailOutlined", "SafetyOutlined", "CheckCircleOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "Step", "RecoveryStep", "Account<PERSON><PERSON><PERSON><PERSON>", "_s", "token", "accountType", "navigate", "form", "useForm", "currentStep", "setCurrentStep", "VERIFY", "REQUEST", "loading", "setLoading", "error", "setError", "accountInfo", "setAccountInfo", "authStatus", "setAuthStatus", "AuthStatus", "IDLE", "verifyToken", "LOADING", "response", "secureApiService", "get", "success", "data", "RESET", "SUCCESS", "message", "ERROR", "err", "_err$response", "_err$response$data", "handleRequestRecovery", "values", "post", "_err$response2", "_err$response2$data", "handleResetPassword", "password", "confirmPassword", "COMPLETE", "_err$response3", "_err$response3$data", "renderRequestForm", "name", "onFinish", "layout", "initialValues", "children", "<PERSON><PERSON>", "label", "rules", "required", "type", "prefix", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "size", "className", "getFieldValue", "onClick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "htmlType", "block", "renderVerifyStep", "description", "showIcon", "renderResetForm", "min", "pattern", "Password", "dependencies", "validator", "_", "value", "Promise", "resolve", "reject", "Error", "renderCompleteStep", "status", "title", "subTitle", "extra", "renderStepContent", "level", "current", "icon", "AuthFeedback", "showProgress", "autoHideDuration", "details", "onClose", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Form, Input, Button, Card, Steps, Typography, Alert, Divider, Result } from 'antd';\nimport { LockOutlined, MailOutlined, SafetyOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport authService from '../services/authService';\n// AuthFeedback component removed - using Ant Design components for feedback\nimport { ApiResponse } from '../types/api';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Step } = Steps;\n\nenum RecoveryStep {\n  REQUEST = 0,\n  VERIFY = 1,\n  RESET = 2,\n  COMPLETE = 3,\n}\n\ninterface AccountInfo {\n  email: string;\n  name: string;\n  hasTwoFactor: boolean;\n}\n\nconst AccountRecovery: React.FC = () => {\n  const { token, accountType } = useParams<{ token?: string; accountType?: string }>();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n\n  const [currentStep, setCurrentStep] = useState<RecoveryStep>(\n    token ? RecoveryStep.VERIFY : RecoveryStep.REQUEST\n  );\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [accountInfo, setAccountInfo] = useState<AccountInfo | null>(null);\n  const [authStatus, setAuthStatus] = useState<AuthStatus>(AuthStatus.IDLE);\n\n  // Verify token on component mount if token is provided\n  useEffect(() => {\n    if (token && accountType) {\n      verifyToken();\n    }\n  }, [token, accountType]);\n\n  // Verify recovery token\n  const verifyToken = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n\n      const response = await secureApiService.get<ApiResponse<AccountInfo>>(`/account-recovery/${accountType}/${token}/verify`);\n\n      if (response.success && response.data) {\n        setAccountInfo(response.data);\n        setCurrentStep(RecoveryStep.RESET);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Invalid or expired recovery token');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'Failed to verify recovery token');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Request account recovery\n  const handleRequestRecovery = async (values: { email: string; accountType: string }) => {\n    try {\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n\n      const response = await secureApiService.post<ApiResponse>('/account-recovery/request', values);\n\n      if (response.success) {\n        setCurrentStep(RecoveryStep.VERIFY);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Failed to request account recovery');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'Failed to request account recovery');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset password\n  const handleResetPassword = async (values: { password: string; confirmPassword: string }) => {\n    try {\n      if (values.password !== values.confirmPassword) {\n        setError('Passwords do not match');\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n      setAuthStatus(AuthStatus.LOADING);\n\n      const response = await secureApiService.post<ApiResponse>(\n        `/account-recovery/${accountType}/${token}/reset-password`,\n        { password: values.password }\n      );\n\n      if (response.success) {\n        setCurrentStep(RecoveryStep.COMPLETE);\n        setAuthStatus(AuthStatus.SUCCESS);\n      } else {\n        setError(response.message || 'Failed to reset password');\n        setAuthStatus(AuthStatus.ERROR);\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'Failed to reset password');\n      setAuthStatus(AuthStatus.ERROR);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Render request recovery form\n  const renderRequestForm = () => (\n    <Form\n      form={form}\n      name=\"recoveryRequest\"\n      onFinish={handleRequestRecovery}\n      layout=\"vertical\"\n      initialValues={{ accountType: 'user' }}\n    >\n      <Form.Item\n        name=\"email\"\n        label=\"Email Address\"\n        rules={[\n          { required: true, message: 'Please enter your email address' },\n          { type: 'email', message: 'Please enter a valid email address' },\n        ]}\n      >\n        <Input prefix={<MailOutlined />} placeholder=\"Email\" size=\"large\" />\n      </Form.Item>\n\n      <Form.Item name=\"accountType\" label=\"Account Type\">\n        <div className=\"flex space-x-4\">\n          <Button\n            type={form.getFieldValue('accountType') === 'user' ? 'primary' : 'default'}\n            onClick={() => form.setFieldsValue({ accountType: 'user' })}\n            className=\"flex-1\"\n          >\n            User Account\n          </Button>\n          <Button\n            type={form.getFieldValue('accountType') === 'admin' ? 'primary' : 'default'}\n            onClick={() => form.setFieldsValue({ accountType: 'admin' })}\n            className=\"flex-1\"\n          >\n            Admin Account\n          </Button>\n        </div>\n      </Form.Item>\n\n      <Form.Item>\n        <Button\n          type=\"primary\"\n          htmlType=\"submit\"\n          size=\"large\"\n          block\n          loading={loading}\n          className=\"mt-4\"\n        >\n          Request Recovery\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n\n  // Render verify token step\n  const renderVerifyStep = () => (\n    <div className=\"text-center\">\n      <Alert\n        message=\"Check Your Email\"\n        description=\"We've sent recovery instructions to your email address. Please check your inbox and follow the instructions.\"\n        type=\"info\"\n        showIcon\n        className=\"mb-6\"\n      />\n      <Paragraph>\n        <Text type=\"secondary\">\n          If you don't receive an email within a few minutes, check your spam folder or request a new recovery link.\n        </Text>\n      </Paragraph>\n      <Button\n        onClick={() => setCurrentStep(RecoveryStep.REQUEST)}\n        className=\"mt-4\"\n      >\n        Request New Link\n      </Button>\n    </div>\n  );\n\n  // Render reset password form\n  const renderResetForm = () => (\n    <Form\n      form={form}\n      name=\"resetPassword\"\n      onFinish={handleResetPassword}\n      layout=\"vertical\"\n    >\n      {accountInfo && (\n        <Alert\n          message={`Hello, ${accountInfo.name}`}\n          description=\"You can now reset your password. Please choose a strong password that you haven't used before.\"\n          type=\"success\"\n          showIcon\n          className=\"mb-6\"\n        />\n      )}\n\n      <Form.Item\n        name=\"password\"\n        label=\"New Password\"\n        rules={[\n          { required: true, message: 'Please enter a new password' },\n          { min: 8, message: 'Password must be at least 8 characters long' },\n          {\n            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/,\n            message: 'Password must include uppercase, lowercase, number, and special character',\n          },\n        ]}\n      >\n        <Input.Password prefix={<LockOutlined />} placeholder=\"New Password\" size=\"large\" />\n      </Form.Item>\n\n      <Form.Item\n        name=\"confirmPassword\"\n        label=\"Confirm Password\"\n        dependencies={['password']}\n        rules={[\n          { required: true, message: 'Please confirm your password' },\n          ({ getFieldValue }) => ({\n            validator(_, value) {\n              if (!value || getFieldValue('password') === value) {\n                return Promise.resolve();\n              }\n              return Promise.reject(new Error('The two passwords do not match'));\n            },\n          }),\n        ]}\n      >\n        <Input.Password prefix={<LockOutlined />} placeholder=\"Confirm Password\" size=\"large\" />\n      </Form.Item>\n\n      <Form.Item>\n        <Button\n          type=\"primary\"\n          htmlType=\"submit\"\n          size=\"large\"\n          block\n          loading={loading}\n          className=\"mt-4\"\n        >\n          Reset Password\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n\n  // Render completion step\n  const renderCompleteStep = () => (\n    <Result\n      status=\"success\"\n      title=\"Password Reset Successful\"\n      subTitle=\"Your password has been reset successfully. You can now log in with your new password.\"\n      extra={[\n        <Button\n          type=\"primary\"\n          key=\"login\"\n          onClick={() => navigate(accountType === 'admin' ? '/admin/secure-login' : '/login')}\n        >\n          Go to Login\n        </Button>,\n      ]}\n    />\n  );\n\n  // Render current step content\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case RecoveryStep.REQUEST:\n        return renderRequestForm();\n      case RecoveryStep.VERIFY:\n        return renderVerifyStep();\n      case RecoveryStep.RESET:\n        return renderResetForm();\n      case RecoveryStep.COMPLETE:\n        return renderCompleteStep();\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-100 p-4\">\n      <Card className=\"w-full max-w-md shadow-lg\">\n        <div className=\"text-center mb-6\">\n          <Title level={3}>Account Recovery</Title>\n          <Text type=\"secondary\">\n            Recover your account access securely\n          </Text>\n        </div>\n\n        <Steps current={currentStep} className=\"mb-8\">\n          <Step title=\"Request\" icon={<MailOutlined />} />\n          <Step title=\"Verify\" icon={<SafetyOutlined />} />\n          <Step title=\"Reset\" icon={<LockOutlined />} />\n          <Step title=\"Complete\" icon={<CheckCircleOutlined />} />\n        </Steps>\n\n        {/* Authentication feedback */}\n        {authStatus === AuthStatus.LOADING && (\n          <AuthFeedback\n            status={AuthStatus.LOADING}\n            title=\"Processing\"\n            message=\"Please wait while we process your request...\"\n            showProgress={false}\n          />\n        )}\n\n        {authStatus === AuthStatus.SUCCESS && currentStep !== RecoveryStep.COMPLETE && (\n          <AuthFeedback\n            status={AuthStatus.SUCCESS}\n            title=\"Success\"\n            message={\n              currentStep === RecoveryStep.VERIFY\n                ? \"Recovery instructions sent to your email\"\n                : \"Verification successful\"\n            }\n            autoHideDuration={3000}\n          />\n        )}\n\n        {authStatus === AuthStatus.ERROR && (\n          <AuthFeedback\n            status={AuthStatus.ERROR}\n            title=\"Error\"\n            message={error || \"An error occurred\"}\n            details=\"Please try again or contact support if the problem persists.\"\n            onClose={() => {\n              setError(null);\n              setAuthStatus(AuthStatus.IDLE);\n            }}\n          />\n        )}\n\n        <Divider />\n\n        {renderStepContent()}\n      </Card>\n    </div>\n  );\n};\n\nexport default AccountRecovery;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,QAAQ,MAAM;AAC3F,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,mBAAmB,QAAQ,mBAAmB;;AAEnG;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGZ,UAAU;AAC7C,MAAM;EAAEa;AAAK,CAAC,GAAGd,KAAK;AAAC,IAElBe,YAAY,0BAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAA,OAAZA,YAAY;AAAA,EAAZA,YAAY;AAajB,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,KAAK;IAAEC;EAAY,CAAC,GAAGzB,SAAS,CAA2C,CAAC;EACpF,MAAM0B,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,IAAI,CAAC,GAAGzB,IAAI,CAAC0B,OAAO,CAAC,CAAC;EAE7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAC5C0B,KAAK,GAAGH,YAAY,CAACU,MAAM,GAAGV,YAAY,CAACW,OAC7C,CAAC;EACD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAa2C,UAAU,CAACC,IAAI,CAAC;;EAEzE;EACA3C,SAAS,CAAC,MAAM;IACd,IAAIyB,KAAK,IAAIC,WAAW,EAAE;MACxBkB,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACnB,KAAK,EAAEC,WAAW,CAAC,CAAC;;EAExB;EACA,MAAMkB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdI,aAAa,CAACC,UAAU,CAACG,OAAO,CAAC;MAEjC,MAAMC,QAAQ,GAAG,MAAMC,gBAAgB,CAACC,GAAG,CAA2B,qBAAqBtB,WAAW,IAAID,KAAK,SAAS,CAAC;MAEzH,IAAIqB,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;QACrCX,cAAc,CAACO,QAAQ,CAACI,IAAI,CAAC;QAC7BnB,cAAc,CAACT,YAAY,CAAC6B,KAAK,CAAC;QAClCV,aAAa,CAACC,UAAU,CAACU,OAAO,CAAC;MACnC,CAAC,MAAM;QACLf,QAAQ,CAACS,QAAQ,CAACO,OAAO,IAAI,mCAAmC,CAAC;QACjEZ,aAAa,CAACC,UAAU,CAACY,KAAK,CAAC;MACjC;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBpB,QAAQ,CAAC,EAAAmB,aAAA,GAAAD,GAAG,CAACT,QAAQ,cAAAU,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcN,IAAI,cAAAO,kBAAA,uBAAlBA,kBAAA,CAAoBJ,OAAO,KAAIE,GAAG,CAACF,OAAO,IAAI,iCAAiC,CAAC;MACzFZ,aAAa,CAACC,UAAU,CAACY,KAAK,CAAC;IACjC,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuB,qBAAqB,GAAG,MAAOC,MAA8C,IAAK;IACtF,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdI,aAAa,CAACC,UAAU,CAACG,OAAO,CAAC;MAEjC,MAAMC,QAAQ,GAAG,MAAMC,gBAAgB,CAACa,IAAI,CAAc,2BAA2B,EAAED,MAAM,CAAC;MAE9F,IAAIb,QAAQ,CAACG,OAAO,EAAE;QACpBlB,cAAc,CAACT,YAAY,CAACU,MAAM,CAAC;QACnCS,aAAa,CAACC,UAAU,CAACU,OAAO,CAAC;MACnC,CAAC,MAAM;QACLf,QAAQ,CAACS,QAAQ,CAACO,OAAO,IAAI,oCAAoC,CAAC;QAClEZ,aAAa,CAACC,UAAU,CAACY,KAAK,CAAC;MACjC;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAM,cAAA,EAAAC,mBAAA;MACjBzB,QAAQ,CAAC,EAAAwB,cAAA,GAAAN,GAAG,CAACT,QAAQ,cAAAe,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcX,IAAI,cAAAY,mBAAA,uBAAlBA,mBAAA,CAAoBT,OAAO,KAAIE,GAAG,CAACF,OAAO,IAAI,oCAAoC,CAAC;MAC5FZ,aAAa,CAACC,UAAU,CAACY,KAAK,CAAC;IACjC,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4B,mBAAmB,GAAG,MAAOJ,MAAqD,IAAK;IAC3F,IAAI;MACF,IAAIA,MAAM,CAACK,QAAQ,KAAKL,MAAM,CAACM,eAAe,EAAE;QAC9C5B,QAAQ,CAAC,wBAAwB,CAAC;QAClC;MACF;MAEAF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdI,aAAa,CAACC,UAAU,CAACG,OAAO,CAAC;MAEjC,MAAMC,QAAQ,GAAG,MAAMC,gBAAgB,CAACa,IAAI,CAC1C,qBAAqBlC,WAAW,IAAID,KAAK,iBAAiB,EAC1D;QAAEuC,QAAQ,EAAEL,MAAM,CAACK;MAAS,CAC9B,CAAC;MAED,IAAIlB,QAAQ,CAACG,OAAO,EAAE;QACpBlB,cAAc,CAACT,YAAY,CAAC4C,QAAQ,CAAC;QACrCzB,aAAa,CAACC,UAAU,CAACU,OAAO,CAAC;MACnC,CAAC,MAAM;QACLf,QAAQ,CAACS,QAAQ,CAACO,OAAO,IAAI,0BAA0B,CAAC;QACxDZ,aAAa,CAACC,UAAU,CAACY,KAAK,CAAC;MACjC;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAY,cAAA,EAAAC,mBAAA;MACjB/B,QAAQ,CAAC,EAAA8B,cAAA,GAAAZ,GAAG,CAACT,QAAQ,cAAAqB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjB,IAAI,cAAAkB,mBAAA,uBAAlBA,mBAAA,CAAoBf,OAAO,KAAIE,GAAG,CAACF,OAAO,IAAI,0BAA0B,CAAC;MAClFZ,aAAa,CAACC,UAAU,CAACY,KAAK,CAAC;IACjC,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkC,iBAAiB,GAAGA,CAAA,kBACxBpD,OAAA,CAACd,IAAI;IACHyB,IAAI,EAAEA,IAAK;IACX0C,IAAI,EAAC,iBAAiB;IACtBC,QAAQ,EAAEb,qBAAsB;IAChCc,MAAM,EAAC,UAAU;IACjBC,aAAa,EAAE;MAAE/C,WAAW,EAAE;IAAO,CAAE;IAAAgD,QAAA,gBAEvCzD,OAAA,CAACd,IAAI,CAACwE,IAAI;MACRL,IAAI,EAAC,OAAO;MACZM,KAAK,EAAC,eAAe;MACrBC,KAAK,EAAE,CACL;QAAEC,QAAQ,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAAkC,CAAC,EAC9D;QAAE0B,IAAI,EAAE,OAAO;QAAE1B,OAAO,EAAE;MAAqC,CAAC,CAChE;MAAAqB,QAAA,eAEFzD,OAAA,CAACb,KAAK;QAAC4E,MAAM,eAAE/D,OAAA,CAACJ,YAAY;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACC,WAAW,EAAC,OAAO;QAACC,IAAI,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAEZnE,OAAA,CAACd,IAAI,CAACwE,IAAI;MAACL,IAAI,EAAC,aAAa;MAACM,KAAK,EAAC,cAAc;MAAAF,QAAA,eAChDzD,OAAA;QAAKsE,SAAS,EAAC,gBAAgB;QAAAb,QAAA,gBAC7BzD,OAAA,CAACZ,MAAM;UACL0E,IAAI,EAAEnD,IAAI,CAAC4D,aAAa,CAAC,aAAa,CAAC,KAAK,MAAM,GAAG,SAAS,GAAG,SAAU;UAC3EC,OAAO,EAAEA,CAAA,KAAM7D,IAAI,CAAC8D,cAAc,CAAC;YAAEhE,WAAW,EAAE;UAAO,CAAC,CAAE;UAC5D6D,SAAS,EAAC,QAAQ;UAAAb,QAAA,EACnB;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnE,OAAA,CAACZ,MAAM;UACL0E,IAAI,EAAEnD,IAAI,CAAC4D,aAAa,CAAC,aAAa,CAAC,KAAK,OAAO,GAAG,SAAS,GAAG,SAAU;UAC5EC,OAAO,EAAEA,CAAA,KAAM7D,IAAI,CAAC8D,cAAc,CAAC;YAAEhE,WAAW,EAAE;UAAQ,CAAC,CAAE;UAC7D6D,SAAS,EAAC,QAAQ;UAAAb,QAAA,EACnB;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEZnE,OAAA,CAACd,IAAI,CAACwE,IAAI;MAAAD,QAAA,eACRzD,OAAA,CAACZ,MAAM;QACL0E,IAAI,EAAC,SAAS;QACdY,QAAQ,EAAC,QAAQ;QACjBL,IAAI,EAAC,OAAO;QACZM,KAAK;QACL1D,OAAO,EAAEA,OAAQ;QACjBqD,SAAS,EAAC,MAAM;QAAAb,QAAA,EACjB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACP;;EAED;EACA,MAAMS,gBAAgB,GAAGA,CAAA,kBACvB5E,OAAA;IAAKsE,SAAS,EAAC,aAAa;IAAAb,QAAA,gBAC1BzD,OAAA,CAACR,KAAK;MACJ4C,OAAO,EAAC,kBAAkB;MAC1ByC,WAAW,EAAC,8GAA8G;MAC1Hf,IAAI,EAAC,MAAM;MACXgB,QAAQ;MACRR,SAAS,EAAC;IAAM;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eACFnE,OAAA,CAACG,SAAS;MAAAsD,QAAA,eACRzD,OAAA,CAACE,IAAI;QAAC4D,IAAI,EAAC,WAAW;QAAAL,QAAA,EAAC;MAEvB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACZnE,OAAA,CAACZ,MAAM;MACLoF,OAAO,EAAEA,CAAA,KAAM1D,cAAc,CAACT,YAAY,CAACW,OAAO,CAAE;MACpDsD,SAAS,EAAC,MAAM;MAAAb,QAAA,EACjB;IAED;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;;EAED;EACA,MAAMY,eAAe,GAAGA,CAAA,kBACtB/E,OAAA,CAACd,IAAI;IACHyB,IAAI,EAAEA,IAAK;IACX0C,IAAI,EAAC,eAAe;IACpBC,QAAQ,EAAER,mBAAoB;IAC9BS,MAAM,EAAC,UAAU;IAAAE,QAAA,GAEhBpC,WAAW,iBACVrB,OAAA,CAACR,KAAK;MACJ4C,OAAO,EAAE,UAAUf,WAAW,CAACgC,IAAI,EAAG;MACtCwB,WAAW,EAAC,gGAAgG;MAC5Gf,IAAI,EAAC,SAAS;MACdgB,QAAQ;MACRR,SAAS,EAAC;IAAM;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACF,eAEDnE,OAAA,CAACd,IAAI,CAACwE,IAAI;MACRL,IAAI,EAAC,UAAU;MACfM,KAAK,EAAC,cAAc;MACpBC,KAAK,EAAE,CACL;QAAEC,QAAQ,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAA8B,CAAC,EAC1D;QAAE4C,GAAG,EAAE,CAAC;QAAE5C,OAAO,EAAE;MAA8C,CAAC,EAClE;QACE6C,OAAO,EAAE,sEAAsE;QAC/E7C,OAAO,EAAE;MACX,CAAC,CACD;MAAAqB,QAAA,eAEFzD,OAAA,CAACb,KAAK,CAAC+F,QAAQ;QAACnB,MAAM,eAAE/D,OAAA,CAACL,YAAY;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACC,WAAW,EAAC,cAAc;QAACC,IAAI,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CAAC,eAEZnE,OAAA,CAACd,IAAI,CAACwE,IAAI;MACRL,IAAI,EAAC,iBAAiB;MACtBM,KAAK,EAAC,kBAAkB;MACxBwB,YAAY,EAAE,CAAC,UAAU,CAAE;MAC3BvB,KAAK,EAAE,CACL;QAAEC,QAAQ,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAA+B,CAAC,EAC3D,CAAC;QAAEmC;MAAc,CAAC,MAAM;QACtBa,SAASA,CAACC,CAAC,EAAEC,KAAK,EAAE;UAClB,IAAI,CAACA,KAAK,IAAIf,aAAa,CAAC,UAAU,CAAC,KAAKe,KAAK,EAAE;YACjD,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;UAC1B;UACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpE;MACF,CAAC,CAAC,CACF;MAAAjC,QAAA,eAEFzD,OAAA,CAACb,KAAK,CAAC+F,QAAQ;QAACnB,MAAM,eAAE/D,OAAA,CAACL,YAAY;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACC,WAAW,EAAC,kBAAkB;QAACC,IAAI,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,eAEZnE,OAAA,CAACd,IAAI,CAACwE,IAAI;MAAAD,QAAA,eACRzD,OAAA,CAACZ,MAAM;QACL0E,IAAI,EAAC,SAAS;QACdY,QAAQ,EAAC,QAAQ;QACjBL,IAAI,EAAC,OAAO;QACZM,KAAK;QACL1D,OAAO,EAAEA,OAAQ;QACjBqD,SAAS,EAAC,MAAM;QAAAb,QAAA,EACjB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACP;;EAED;EACA,MAAMwB,kBAAkB,GAAGA,CAAA,kBACzB3F,OAAA,CAACN,MAAM;IACLkG,MAAM,EAAC,SAAS;IAChBC,KAAK,EAAC,2BAA2B;IACjCC,QAAQ,EAAC,uFAAuF;IAChGC,KAAK,EAAE,cACL/F,OAAA,CAACZ,MAAM;MACL0E,IAAI,EAAC,SAAS;MAEdU,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAACD,WAAW,KAAK,OAAO,GAAG,qBAAqB,GAAG,QAAQ,CAAE;MAAAgD,QAAA,EACrF;IAED,GAJM,OAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIL,CAAC;EACT;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;;EAED;EACA,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQnF,WAAW;MACjB,KAAKR,YAAY,CAACW,OAAO;QACvB,OAAOoC,iBAAiB,CAAC,CAAC;MAC5B,KAAK/C,YAAY,CAACU,MAAM;QACtB,OAAO6D,gBAAgB,CAAC,CAAC;MAC3B,KAAKvE,YAAY,CAAC6B,KAAK;QACrB,OAAO6C,eAAe,CAAC,CAAC;MAC1B,KAAK1E,YAAY,CAAC4C,QAAQ;QACxB,OAAO0C,kBAAkB,CAAC,CAAC;MAC7B;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE3F,OAAA;IAAKsE,SAAS,EAAC,+DAA+D;IAAAb,QAAA,eAC5EzD,OAAA,CAACX,IAAI;MAACiF,SAAS,EAAC,2BAA2B;MAAAb,QAAA,gBACzCzD,OAAA;QAAKsE,SAAS,EAAC,kBAAkB;QAAAb,QAAA,gBAC/BzD,OAAA,CAACC,KAAK;UAACgG,KAAK,EAAE,CAAE;UAAAxC,QAAA,EAAC;QAAgB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzCnE,OAAA,CAACE,IAAI;UAAC4D,IAAI,EAAC,WAAW;UAAAL,QAAA,EAAC;QAEvB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENnE,OAAA,CAACV,KAAK;QAAC4G,OAAO,EAAErF,WAAY;QAACyD,SAAS,EAAC,MAAM;QAAAb,QAAA,gBAC3CzD,OAAA,CAACI,IAAI;UAACyF,KAAK,EAAC,SAAS;UAACM,IAAI,eAAEnG,OAAA,CAACJ,YAAY;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDnE,OAAA,CAACI,IAAI;UAACyF,KAAK,EAAC,QAAQ;UAACM,IAAI,eAAEnG,OAAA,CAACH,cAAc;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDnE,OAAA,CAACI,IAAI;UAACyF,KAAK,EAAC,OAAO;UAACM,IAAI,eAAEnG,OAAA,CAACL,YAAY;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CnE,OAAA,CAACI,IAAI;UAACyF,KAAK,EAAC,UAAU;UAACM,IAAI,eAAEnG,OAAA,CAACF,mBAAmB;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,EAGP5C,UAAU,KAAKE,UAAU,CAACG,OAAO,iBAChC5B,OAAA,CAACoG,YAAY;QACXR,MAAM,EAAEnE,UAAU,CAACG,OAAQ;QAC3BiE,KAAK,EAAC,YAAY;QAClBzD,OAAO,EAAC,8CAA8C;QACtDiE,YAAY,EAAE;MAAM;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACF,EAEA5C,UAAU,KAAKE,UAAU,CAACU,OAAO,IAAItB,WAAW,KAAKR,YAAY,CAAC4C,QAAQ,iBACzEjD,OAAA,CAACoG,YAAY;QACXR,MAAM,EAAEnE,UAAU,CAACU,OAAQ;QAC3B0D,KAAK,EAAC,SAAS;QACfzD,OAAO,EACLvB,WAAW,KAAKR,YAAY,CAACU,MAAM,GAC/B,0CAA0C,GAC1C,yBACL;QACDuF,gBAAgB,EAAE;MAAK;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACF,EAEA5C,UAAU,KAAKE,UAAU,CAACY,KAAK,iBAC9BrC,OAAA,CAACoG,YAAY;QACXR,MAAM,EAAEnE,UAAU,CAACY,KAAM;QACzBwD,KAAK,EAAC,OAAO;QACbzD,OAAO,EAAEjB,KAAK,IAAI,mBAAoB;QACtCoF,OAAO,EAAC,8DAA8D;QACtEC,OAAO,EAAEA,CAAA,KAAM;UACbpF,QAAQ,CAAC,IAAI,CAAC;UACdI,aAAa,CAACC,UAAU,CAACC,IAAI,CAAC;QAChC;MAAE;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,eAEDnE,OAAA,CAACP,OAAO;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEV6B,iBAAiB,CAAC,CAAC;IAAA;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5D,EAAA,CAnVID,eAAyB;EAAA,QACEtB,SAAS,EACvBC,WAAW,EACbC,IAAI,CAAC0B,OAAO;AAAA;AAAA6F,EAAA,GAHvBnG,eAAyB;AAqV/B,eAAeA,eAAe;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}