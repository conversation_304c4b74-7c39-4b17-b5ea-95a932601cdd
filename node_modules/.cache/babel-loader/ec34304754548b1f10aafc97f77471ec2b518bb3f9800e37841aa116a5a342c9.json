{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport authService from '../services/authService';\nexport const useAdminApi = () => {\n  _s();\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const fetchStats = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Fetch admin statistics\n      const response = await authService.request('GET', '/admin/stats');\n      setStats(response.data);\n    } catch (err) {\n      console.error('Error fetching admin stats:', err);\n      setError(err.message || 'Failed to fetch admin statistics');\n\n      // Set default stats if API fails\n      setStats({\n        totalScholarships: 0,\n        totalSubscribers: 0,\n        totalMessages: 0,\n        recentActivity: []\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const refreshStats = async () => {\n    await fetchStats();\n  };\n  useEffect(() => {\n    fetchStats();\n  }, []);\n  return {\n    stats,\n    loading,\n    error,\n    refreshStats\n  };\n};\n_s(useAdminApi, \"wn5GnbEWSpy3babVhLIjrKhKWKg=\");", "map": {"version": 3, "names": ["useState", "useEffect", "authService", "useAdminApi", "_s", "stats", "setStats", "loading", "setLoading", "error", "setError", "fetchStats", "response", "request", "data", "err", "console", "message", "totalScholarships", "totalSubscribers", "totalMessages", "recentActivity", "refreshStats"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport authService from '../services/authService';\n\ninterface AdminStats {\n  totalScholarships: number;\n  totalSubscribers: number;\n  totalMessages: number;\n  recentActivity: any[];\n}\n\ninterface UseAdminApiReturn {\n  stats: AdminStats | null;\n  loading: boolean;\n  error: string | null;\n  refreshStats: () => Promise<void>;\n}\n\nexport const useAdminApi = (): UseAdminApiReturn => {\n  const [stats, setStats] = useState<AdminStats | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchStats = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Fetch admin statistics\n      const response = await authService.request('GET', '/admin/stats') as { data: AdminStats };\n      setStats(response.data);\n    } catch (err: any) {\n      console.error('Error fetching admin stats:', err);\n      setError(err.message || 'Failed to fetch admin statistics');\n      \n      // Set default stats if API fails\n      setStats({\n        totalScholarships: 0,\n        totalSubscribers: 0,\n        totalMessages: 0,\n        recentActivity: []\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const refreshStats = async () => {\n    await fetchStats();\n  };\n\n  useEffect(() => {\n    fetchStats();\n  }, []);\n\n  return {\n    stats,\n    loading,\n    error,\n    refreshStats\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,WAAW,MAAM,yBAAyB;AAgBjD,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAyB;EAAAC,EAAA;EAClD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGN,QAAQ,CAAoB,IAAI,CAAC;EAC3D,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAMW,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAME,QAAQ,GAAG,MAAMV,WAAW,CAACW,OAAO,CAAC,KAAK,EAAE,cAAc,CAAyB;MACzFP,QAAQ,CAACM,QAAQ,CAACE,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACP,KAAK,CAAC,6BAA6B,EAAEM,GAAG,CAAC;MACjDL,QAAQ,CAACK,GAAG,CAACE,OAAO,IAAI,kCAAkC,CAAC;;MAE3D;MACAX,QAAQ,CAAC;QACPY,iBAAiB,EAAE,CAAC;QACpBC,gBAAgB,EAAE,CAAC;QACnBC,aAAa,EAAE,CAAC;QAChBC,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMX,UAAU,CAAC,CAAC;EACpB,CAAC;EAEDV,SAAS,CAAC,MAAM;IACdU,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLN,KAAK;IACLE,OAAO;IACPE,KAAK;IACLa;EACF,CAAC;AACH,CAAC;AAAClB,EAAA,CA3CWD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}