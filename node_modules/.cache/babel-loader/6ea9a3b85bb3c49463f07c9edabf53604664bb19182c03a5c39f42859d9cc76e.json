{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport authService from '../services/authService';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Create context\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [admin, setAdmin] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Clear error\n  const clearError = () => setError(null);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = async () => {\n    try {\n      var _response$data;\n      setIsLoading(true);\n      const response = await authService.getAdminProfile();\n      if (response.success && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.admin) {\n        setAdmin(response.data.admin);\n        setIsAuthenticated(true);\n      } else {\n        setAdmin(null);\n        setIsAuthenticated(false);\n      }\n    } catch (error) {\n      // Not authenticated - this is normal\n      setAdmin(null);\n      setIsAuthenticated(false);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Login function\n  const login = async (email, password) => {\n    try {\n      var _response$data2;\n      setIsLoading(true);\n      setError(null);\n      const response = await authService.adminLogin(email, password);\n      if (response.success && (_response$data2 = response.data) !== null && _response$data2 !== void 0 && _response$data2.admin) {\n        setAdmin(response.data.admin);\n        setIsAuthenticated(true);\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error) {\n      setError(error.message);\n      setAdmin(null);\n      setIsAuthenticated(false);\n      throw error; // Re-throw for component handling\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      setIsLoading(true);\n      await authService.adminLogout();\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Continue with logout even if API call fails\n    } finally {\n      setAdmin(null);\n      setIsAuthenticated(false);\n      setIsLoading(false);\n    }\n  };\n\n  // Refresh authentication status\n  const refreshAuth = async () => {\n    await checkAuthStatus();\n  };\n  const value = {\n    admin,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    clearError,\n    refreshAuth\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"ODqrP8/1Ao9PF5rW2dBCyMsHojk=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "admin", "set<PERSON>d<PERSON>", "isAuthenticated", "setIsAuthenticated", "isLoading", "setIsLoading", "error", "setError", "clearError", "checkAuthStatus", "_response$data", "response", "getAdminProfile", "success", "data", "login", "email", "password", "_response$data2", "adminLogin", "Error", "message", "logout", "adminLogout", "console", "refreshAuth", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport authService from '../services/authService';\n\n// Types\ninterface Admin {\n  id: number;\n  name: string;\n  email: string;\n  role: string;\n  isMainAdmin: boolean;\n  privileges: string[];\n  twoFactorEnabled: boolean;\n}\n\ninterface AuthContextType {\n  // State\n  admin: Admin | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  clearError: () => void;\n  refreshAuth: () => Promise<void>;\n}\n\n// Create context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Provider component\nexport const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [admin, setAdmin] = useState<Admin | null>(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Clear error\n  const clearError = () => setError(null);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  // Check authentication status\n  const checkAuthStatus = async () => {\n    try {\n      setIsLoading(true);\n      const response = await authService.getAdminProfile();\n      \n      if (response.success && response.data?.admin) {\n        setAdmin(response.data.admin);\n        setIsAuthenticated(true);\n      } else {\n        setAdmin(null);\n        setIsAuthenticated(false);\n      }\n    } catch (error: any) {\n      // Not authenticated - this is normal\n      setAdmin(null);\n      setIsAuthenticated(false);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Login function\n  const login = async (email: string, password: string) => {\n    try {\n      setIsLoading(true);\n      setError(null);\n\n      const response = await authService.adminLogin(email, password);\n      \n      if (response.success && response.data?.admin) {\n        setAdmin(response.data.admin);\n        setIsAuthenticated(true);\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error: any) {\n      setError(error.message);\n      setAdmin(null);\n      setIsAuthenticated(false);\n      throw error; // Re-throw for component handling\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      setIsLoading(true);\n      await authService.adminLogout();\n    } catch (error: any) {\n      console.error('Logout error:', error);\n      // Continue with logout even if API call fails\n    } finally {\n      setAdmin(null);\n      setIsAuthenticated(false);\n      setIsLoading(false);\n    }\n  };\n\n  // Refresh authentication status\n  const refreshAuth = async () => {\n    await checkAuthStatus();\n  };\n\n  const value: AuthContextType = {\n    admin,\n    isAuthenticated,\n    isLoading,\n    error,\n    login,\n    logout,\n    clearError,\n    refreshAuth\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,OAAOC,WAAW,MAAM,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAyBA;AACA,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;;AAEzE;AACA,OAAO,MAAMC,YAA+C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAe,IAAI,CAAC;EACtD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAMkB,UAAU,GAAGA,CAAA,KAAMD,QAAQ,CAAC,IAAI,CAAC;;EAEvC;EACAhB,SAAS,CAAC,MAAM;IACdkB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MAAA,IAAAC,cAAA;MACFL,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMM,QAAQ,GAAG,MAAMnB,WAAW,CAACoB,eAAe,CAAC,CAAC;MAEpD,IAAID,QAAQ,CAACE,OAAO,KAAAH,cAAA,GAAIC,QAAQ,CAACG,IAAI,cAAAJ,cAAA,eAAbA,cAAA,CAAeV,KAAK,EAAE;QAC5CC,QAAQ,CAACU,QAAQ,CAACG,IAAI,CAACd,KAAK,CAAC;QAC7BG,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLF,QAAQ,CAAC,IAAI,CAAC;QACdE,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOG,KAAU,EAAE;MACnB;MACAL,QAAQ,CAAC,IAAI,CAAC;MACdE,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,SAAS;MACRE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMU,KAAK,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAK;IACvD,IAAI;MAAA,IAAAC,eAAA;MACFb,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMI,QAAQ,GAAG,MAAMnB,WAAW,CAAC2B,UAAU,CAACH,KAAK,EAAEC,QAAQ,CAAC;MAE9D,IAAIN,QAAQ,CAACE,OAAO,KAAAK,eAAA,GAAIP,QAAQ,CAACG,IAAI,cAAAI,eAAA,eAAbA,eAAA,CAAelB,KAAK,EAAE;QAC5CC,QAAQ,CAACU,QAAQ,CAACG,IAAI,CAACd,KAAK,CAAC;QAC7BG,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,MAAM;QACL,MAAM,IAAIiB,KAAK,CAACT,QAAQ,CAACU,OAAO,IAAI,cAAc,CAAC;MACrD;IACF,CAAC,CAAC,OAAOf,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAACe,OAAO,CAAC;MACvBpB,QAAQ,CAAC,IAAI,CAAC;MACdE,kBAAkB,CAAC,KAAK,CAAC;MACzB,MAAMG,KAAK,CAAC,CAAC;IACf,CAAC,SAAS;MACRD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMiB,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACFjB,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMb,WAAW,CAAC+B,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC,OAAOjB,KAAU,EAAE;MACnBkB,OAAO,CAAClB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC,SAAS;MACRL,QAAQ,CAAC,IAAI,CAAC;MACdE,kBAAkB,CAAC,KAAK,CAAC;MACzBE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMoB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMhB,eAAe,CAAC,CAAC;EACzB,CAAC;EAED,MAAMiB,KAAsB,GAAG;IAC7B1B,KAAK;IACLE,eAAe;IACfE,SAAS;IACTE,KAAK;IACLS,KAAK;IACLO,MAAM;IACNd,UAAU;IACViB;EACF,CAAC;EAED,oBACE/B,OAAA,CAACC,WAAW,CAACgC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA5B,QAAA,EAChCA;EAAQ;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAhC,EAAA,CAlGaF,YAA+C;AAAAmC,EAAA,GAA/CnC,YAA+C;AAmG5D,OAAO,MAAMoC,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAG9C,UAAU,CAACM,WAAW,CAAC;EACvC,IAAIwC,OAAO,KAAKvC,SAAS,EAAE;IACzB,MAAM,IAAIwB,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOe,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAetC,WAAW;AAAC,IAAAqC,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}