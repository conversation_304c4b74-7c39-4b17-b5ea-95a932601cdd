{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx\";\nimport React from 'react';\nimport { Card, Typography, Alert } from 'antd';\nimport { LockOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst AccountRecovery = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(LockOutlined, {\n            className: \"text-4xl text-blue-500 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            children: \"Account Recovery\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"Feature Under Development\",\n          description: \"Account recovery functionality is being updated to work with the new secure authentication system. Please contact your system administrator for account recovery assistance.\",\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = AccountRecovery;\nexport default AccountRecovery;\nvar _c;\n$RefreshReg$(_c, \"AccountRecovery\");", "map": {"version": 3, "names": ["React", "Card", "Typography", "<PERSON><PERSON>", "LockOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Account<PERSON><PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "message", "description", "type", "showIcon", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, Typography, Alert } from 'antd';\nimport { LockOutlined } from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst AccountRecovery: React.FC = () => {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <Card className=\"shadow-lg\">\n          <div className=\"text-center mb-6\">\n            <LockOutlined className=\"text-4xl text-blue-500 mb-4\" />\n            <Title level={2}>Account Recovery</Title>\n          </div>\n          \n          <Alert\n            message=\"Feature Under Development\"\n            description=\"Account recovery functionality is being updated to work with the new secure authentication system. Please contact your system administrator for account recovery assistance.\"\n            type=\"info\"\n            showIcon\n          />\n        </Card>\n      </div>\n    </div>\n  );\n};\n\nexport default AccountRecovery;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,UAAU,EAAEC,KAAK,QAAQ,MAAM;AAC9C,SAASC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGN,UAAU;AAElC,MAAMO,eAAyB,GAAGA,CAAA,KAAM;EACtC,oBACEH,OAAA;IAAKI,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAClGL,OAAA;MAAKI,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxCL,OAAA,CAACL,IAAI;QAACS,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACzBL,OAAA;UAAKI,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BL,OAAA,CAACF,YAAY;YAACM,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDT,OAAA,CAACC,KAAK;YAACS,KAAK,EAAE,CAAE;YAAAL,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAENT,OAAA,CAACH,KAAK;UACJc,OAAO,EAAC,2BAA2B;UACnCC,WAAW,EAAC,8KAA8K;UAC1LC,IAAI,EAAC,MAAM;UACXC,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GApBIZ,eAAyB;AAsB/B,eAAeA,eAAe;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}