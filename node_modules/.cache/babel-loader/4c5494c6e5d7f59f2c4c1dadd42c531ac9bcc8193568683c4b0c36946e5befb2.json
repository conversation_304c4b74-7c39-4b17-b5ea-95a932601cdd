{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/SecureAuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport secureApiService from '../services/secureApiService';\nimport { clearAllLocalStorage } from '../utils/clearStorage';\n\n// Define user and admin types\n\n// Define context state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Create context with default values\nconst SecureAuthContext = /*#__PURE__*/createContext({\n  user: null,\n  admin: null,\n  isAuthenticated: false,\n  isAdmin: false,\n  isMainAdmin: false,\n  loading: true,\n  error: null,\n  login: async () => {},\n  adminLogin: async () => {},\n  logout: async () => {},\n  clearError: () => {}\n});\n\n// Create provider component\nexport const SecureAuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [admin, setAdmin] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [isMainAdmin, setIsMainAdmin] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Clear error\n  const clearError = () => setError(null);\n\n  // Check if user is authenticated on mount\n  useEffect(() => {\n    const checkAuthStatus = async () => {\n      try {\n        console.log('🔍 SecureAuthContext: Checking authentication status...');\n        setLoading(true);\n\n        // Clear any old localStorage tokens to prevent conflicts\n        console.log('🧹 SecureAuthContext: Clearing old localStorage authentication data...');\n        clearAllLocalStorage();\n\n        // Try to get admin profile first - this will work if the HTTP-only cookie is present\n        try {\n          console.log('Attempting to get admin profile...');\n          const response = await secureApiService.getAdminProfile();\n          console.log('Admin profile response:', response);\n          if (response.success && response.data && response.data.admin) {\n            console.log('Setting admin state from profile');\n            setAdmin(response.data.admin);\n            setUser(null);\n            setIsAuthenticated(true);\n            setIsAdmin(true);\n            setIsMainAdmin(response.data.admin.isMainAdmin);\n            console.log('Admin authentication successful');\n            setLoading(false);\n            return; // Exit early if admin auth is successful\n          } else {\n            console.log('Admin profile response not successful or missing data');\n          }\n        } catch (adminError) {\n          var _adminError$response;\n          console.log('Admin profile error (this is normal if not logged in):', ((_adminError$response = adminError.response) === null || _adminError$response === void 0 ? void 0 : _adminError$response.status) || adminError.message);\n        }\n\n        // If not an admin, try to get user profile\n        try {\n          console.log('Attempting to get user profile...');\n          const response = await secureApiService.getProfile();\n          console.log('User profile response:', response);\n          if (response.success && response.data && response.data.user) {\n            console.log('Setting user state from profile');\n            setUser(response.data.user);\n            setAdmin(null);\n            setIsAuthenticated(true);\n            setIsAdmin(false);\n            setIsMainAdmin(false);\n            console.log('User authentication successful');\n            setLoading(false);\n            return; // Exit early if user auth is successful\n          } else {\n            console.log('User profile response not successful or missing data');\n          }\n        } catch (userError) {\n          var _userError$response;\n          console.log('User profile error (this is normal if not logged in):', ((_userError$response = userError.response) === null || _userError$response === void 0 ? void 0 : _userError$response.status) || userError.message);\n        }\n\n        // If we get here, we're not authenticated as either admin or user\n        console.log('Not authenticated as either admin or user');\n        setUser(null);\n        setAdmin(null);\n        setIsAuthenticated(false);\n        setIsAdmin(false);\n        setIsMainAdmin(false);\n      } catch (error) {\n        console.error('Error checking authentication status:', error);\n        // Make sure to reset auth state on error\n        setUser(null);\n        setAdmin(null);\n        setIsAuthenticated(false);\n        setIsAdmin(false);\n        setIsMainAdmin(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuthStatus();\n  }, []);\n\n  // Login user\n  const login = async (email, password) => {\n    try {\n      setLoading(true);\n      clearError();\n      const response = await secureApiService.login(email, password);\n      if (response.success && response.data.user) {\n        setUser(response.data.user);\n        setAdmin(null);\n        setIsAuthenticated(true);\n        setIsAdmin(false);\n        setIsMainAdmin(false);\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || 'Login failed');\n      setUser(null);\n      setAdmin(null);\n      setIsAuthenticated(false);\n      setIsAdmin(false);\n      setIsMainAdmin(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Login admin\n  const adminLogin = async (email, password) => {\n    try {\n      setLoading(true);\n      clearError();\n      console.log('Attempting admin login with credentials:', {\n        email\n      });\n      const response = await secureApiService.adminLogin(email, password);\n      console.log('Admin login response:', response);\n      if (response.success && response.data.admin) {\n        console.log('Admin login successful:', response.data.admin);\n\n        // Set admin state\n        setAdmin(response.data.admin);\n        setUser(null);\n        setIsAuthenticated(true);\n        setIsAdmin(true);\n        setIsMainAdmin(response.data.admin.isMainAdmin);\n        console.log('Admin authentication state set successfully');\n      } else {\n        throw new Error(response.message || 'Admin login failed');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Admin login error:', error);\n      setError(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || 'Admin login failed');\n      setUser(null);\n      setAdmin(null);\n      setIsAuthenticated(false);\n      setIsAdmin(false);\n      setIsMainAdmin(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Logout\n  const logout = async () => {\n    try {\n      setLoading(true);\n      clearError();\n      await secureApiService.logout();\n\n      // Clear any localStorage data to ensure clean state\n      clearAllLocalStorage();\n      setUser(null);\n      setAdmin(null);\n      setIsAuthenticated(false);\n      setIsAdmin(false);\n      setIsMainAdmin(false);\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      setError(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message || 'Logout failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(SecureAuthContext.Provider, {\n    value: {\n      user,\n      admin,\n      isAuthenticated,\n      isAdmin,\n      isMainAdmin,\n      loading,\n      error,\n      login,\n      adminLogin,\n      logout,\n      clearError\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 235,\n    columnNumber: 5\n  }, this);\n};\n\n// Create custom hook for using the auth context\n_s(SecureAuthProvider, \"+0neMYI4EPHqHsY/hl6/mbPJwFs=\");\n_c = SecureAuthProvider;\nexport const useSecureAuth = () => {\n  _s2();\n  return useContext(SecureAuthContext);\n};\n_s2(useSecureAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport default SecureAuthContext;\nvar _c;\n$RefreshReg$(_c, \"SecureAuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "secureApiService", "clearAllLocalStorage", "jsxDEV", "_jsxDEV", "SecureAuthContext", "user", "admin", "isAuthenticated", "isAdmin", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "error", "login", "adminLogin", "logout", "clearError", "SecureAuthProvider", "children", "_s", "setUser", "set<PERSON>d<PERSON>", "setIsAuthenticated", "setIsAdmin", "setIsMainAdmin", "setLoading", "setError", "checkAuthStatus", "console", "log", "response", "getAdminProfile", "success", "data", "adminError", "_adminError$response", "status", "message", "getProfile", "userError", "_userError$response", "email", "password", "Error", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "_error$response3", "_error$response3$data", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useSecureAuth", "_s2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/SecureAuthContext.tsx"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';\nimport secureApiService from '../services/secureApiService';\nimport { clearAllLocalStorage } from '../utils/clearStorage';\n\n// Define user and admin types\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n  role: string;\n}\n\ninterface Admin {\n  id: number;\n  name: string;\n  email: string;\n  role: string;\n  isMainAdmin: boolean;\n  privileges: string[];\n}\n\n// Define context state\ninterface AuthContextState {\n  user: User | null;\n  admin: Admin | null;\n  isAuthenticated: boolean;\n  isAdmin: boolean;\n  isMainAdmin: boolean;\n  loading: boolean;\n  error: string | null;\n  login: (email: string, password: string) => Promise<void>;\n  adminLogin: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  clearError: () => void;\n}\n\n// Create context with default values\nconst SecureAuthContext = createContext<AuthContextState>({\n  user: null,\n  admin: null,\n  isAuthenticated: false,\n  isAdmin: false,\n  isMainAdmin: false,\n  loading: true,\n  error: null,\n  login: async () => {},\n  adminLogin: async () => {},\n  logout: async () => {},\n  clearError: () => {},\n});\n\n// Create provider component\nexport const SecureAuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [admin, setAdmin] = useState<Admin | null>(null);\n  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);\n  const [isAdmin, setIsAdmin] = useState<boolean>(false);\n  const [isMainAdmin, setIsMainAdmin] = useState<boolean>(false);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Clear error\n  const clearError = () => setError(null);\n\n  // Check if user is authenticated on mount\n  useEffect(() => {\n    const checkAuthStatus = async () => {\n      try {\n        console.log('🔍 SecureAuthContext: Checking authentication status...');\n        setLoading(true);\n\n        // Clear any old localStorage tokens to prevent conflicts\n        console.log('🧹 SecureAuthContext: Clearing old localStorage authentication data...');\n        clearAllLocalStorage();\n\n        // Try to get admin profile first - this will work if the HTTP-only cookie is present\n        try {\n          console.log('Attempting to get admin profile...');\n          const response = await secureApiService.getAdminProfile();\n          console.log('Admin profile response:', response);\n\n          if (response.success && response.data && response.data.admin) {\n            console.log('Setting admin state from profile');\n            setAdmin(response.data.admin);\n            setUser(null);\n            setIsAuthenticated(true);\n            setIsAdmin(true);\n            setIsMainAdmin(response.data.admin.isMainAdmin);\n            console.log('Admin authentication successful');\n            setLoading(false);\n            return; // Exit early if admin auth is successful\n          } else {\n            console.log('Admin profile response not successful or missing data');\n          }\n        } catch (adminError: any) {\n          console.log('Admin profile error (this is normal if not logged in):', adminError.response?.status || adminError.message);\n        }\n\n        // If not an admin, try to get user profile\n        try {\n          console.log('Attempting to get user profile...');\n          const response = await secureApiService.getProfile();\n          console.log('User profile response:', response);\n\n          if (response.success && response.data && response.data.user) {\n            console.log('Setting user state from profile');\n            setUser(response.data.user);\n            setAdmin(null);\n            setIsAuthenticated(true);\n            setIsAdmin(false);\n            setIsMainAdmin(false);\n            console.log('User authentication successful');\n            setLoading(false);\n            return; // Exit early if user auth is successful\n          } else {\n            console.log('User profile response not successful or missing data');\n          }\n        } catch (userError: any) {\n          console.log('User profile error (this is normal if not logged in):', userError.response?.status || userError.message);\n        }\n\n        // If we get here, we're not authenticated as either admin or user\n        console.log('Not authenticated as either admin or user');\n        setUser(null);\n        setAdmin(null);\n        setIsAuthenticated(false);\n        setIsAdmin(false);\n        setIsMainAdmin(false);\n      } catch (error) {\n        console.error('Error checking authentication status:', error);\n        // Make sure to reset auth state on error\n        setUser(null);\n        setAdmin(null);\n        setIsAuthenticated(false);\n        setIsAdmin(false);\n        setIsMainAdmin(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuthStatus();\n  }, []);\n\n  // Login user\n  const login = async (email: string, password: string) => {\n    try {\n      setLoading(true);\n      clearError();\n\n      const response = await secureApiService.login(email, password);\n\n      if (response.success && response.data.user) {\n        setUser(response.data.user);\n        setAdmin(null);\n        setIsAuthenticated(true);\n        setIsAdmin(false);\n        setIsMainAdmin(false);\n      } else {\n        throw new Error(response.message || 'Login failed');\n      }\n    } catch (error: any) {\n      setError(error.response?.data?.message || error.message || 'Login failed');\n      setUser(null);\n      setAdmin(null);\n      setIsAuthenticated(false);\n      setIsAdmin(false);\n      setIsMainAdmin(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Login admin\n  const adminLogin = async (email: string, password: string) => {\n    try {\n      setLoading(true);\n      clearError();\n\n      console.log('Attempting admin login with credentials:', { email });\n      const response = await secureApiService.adminLogin(email, password);\n      console.log('Admin login response:', response);\n\n      if (response.success && response.data.admin) {\n        console.log('Admin login successful:', response.data.admin);\n\n        // Set admin state\n        setAdmin(response.data.admin);\n        setUser(null);\n        setIsAuthenticated(true);\n        setIsAdmin(true);\n        setIsMainAdmin(response.data.admin.isMainAdmin);\n\n        console.log('Admin authentication state set successfully');\n      } else {\n        throw new Error(response.message || 'Admin login failed');\n      }\n    } catch (error: any) {\n      console.error('Admin login error:', error);\n      setError(error.response?.data?.message || error.message || 'Admin login failed');\n      setUser(null);\n      setAdmin(null);\n      setIsAuthenticated(false);\n      setIsAdmin(false);\n      setIsMainAdmin(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Logout\n  const logout = async () => {\n    try {\n      setLoading(true);\n      clearError();\n\n      await secureApiService.logout();\n\n      // Clear any localStorage data to ensure clean state\n      clearAllLocalStorage();\n\n      setUser(null);\n      setAdmin(null);\n      setIsAuthenticated(false);\n      setIsAdmin(false);\n      setIsMainAdmin(false);\n    } catch (error: any) {\n      setError(error.response?.data?.message || error.message || 'Logout failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <SecureAuthContext.Provider\n      value={{\n        user,\n        admin,\n        isAuthenticated,\n        isAdmin,\n        isMainAdmin,\n        loading,\n        error,\n        login,\n        adminLogin,\n        logout,\n        clearError,\n      }}\n    >\n      {children}\n    </SecureAuthContext.Provider>\n  );\n};\n\n// Create custom hook for using the auth context\nexport const useSecureAuth = () => useContext(SecureAuthContext);\n\nexport default SecureAuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAmB,OAAO;AACxF,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,oBAAoB,QAAQ,uBAAuB;;AAE5D;;AAiBA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAeA;AACA,MAAMC,iBAAiB,gBAAGR,aAAa,CAAmB;EACxDS,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,OAAO,EAAE,KAAK;EACdC,WAAW,EAAE,KAAK;EAClBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,MAAAA,CAAA,KAAY,CAAC,CAAC;EACrBC,UAAU,EAAE,MAAAA,CAAA,KAAY,CAAC,CAAC;EAC1BC,MAAM,EAAE,MAAAA,CAAA,KAAY,CAAC,CAAC;EACtBC,UAAU,EAAEA,CAAA,KAAM,CAAC;AACrB,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,kBAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACb,IAAI,EAAEc,OAAO,CAAC,GAAGtB,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACS,KAAK,EAAEc,QAAQ,CAAC,GAAGvB,QAAQ,CAAe,IAAI,CAAC;EACtD,MAAM,CAACU,eAAe,EAAEc,kBAAkB,CAAC,GAAGxB,QAAQ,CAAU,KAAK,CAAC;EACtE,MAAM,CAACW,OAAO,EAAEc,UAAU,CAAC,GAAGzB,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACY,WAAW,EAAEc,cAAc,CAAC,GAAG1B,QAAQ,CAAU,KAAK,CAAC;EAC9D,MAAM,CAACa,OAAO,EAAEc,UAAU,CAAC,GAAG3B,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAACc,KAAK,EAAEc,QAAQ,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAMkB,UAAU,GAAGA,CAAA,KAAMU,QAAQ,CAAC,IAAI,CAAC;;EAEvC;EACA1B,SAAS,CAAC,MAAM;IACd,MAAM2B,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtEJ,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACAG,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;QACrF3B,oBAAoB,CAAC,CAAC;;QAEtB;QACA,IAAI;UACF0B,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD,MAAMC,QAAQ,GAAG,MAAM7B,gBAAgB,CAAC8B,eAAe,CAAC,CAAC;UACzDH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,QAAQ,CAAC;UAEhD,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAAC1B,KAAK,EAAE;YAC5DqB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;YAC/CR,QAAQ,CAACS,QAAQ,CAACG,IAAI,CAAC1B,KAAK,CAAC;YAC7Ba,OAAO,CAAC,IAAI,CAAC;YACbE,kBAAkB,CAAC,IAAI,CAAC;YACxBC,UAAU,CAAC,IAAI,CAAC;YAChBC,cAAc,CAACM,QAAQ,CAACG,IAAI,CAAC1B,KAAK,CAACG,WAAW,CAAC;YAC/CkB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9CJ,UAAU,CAAC,KAAK,CAAC;YACjB,OAAO,CAAC;UACV,CAAC,MAAM;YACLG,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;UACtE;QACF,CAAC,CAAC,OAAOK,UAAe,EAAE;UAAA,IAAAC,oBAAA;UACxBP,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE,EAAAM,oBAAA,GAAAD,UAAU,CAACJ,QAAQ,cAAAK,oBAAA,uBAAnBA,oBAAA,CAAqBC,MAAM,KAAIF,UAAU,CAACG,OAAO,CAAC;QAC1H;;QAEA;QACA,IAAI;UACFT,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAChD,MAAMC,QAAQ,GAAG,MAAM7B,gBAAgB,CAACqC,UAAU,CAAC,CAAC;UACpDV,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAAC;UAE/C,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAAC3B,IAAI,EAAE;YAC3DsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9CT,OAAO,CAACU,QAAQ,CAACG,IAAI,CAAC3B,IAAI,CAAC;YAC3Be,QAAQ,CAAC,IAAI,CAAC;YACdC,kBAAkB,CAAC,IAAI,CAAC;YACxBC,UAAU,CAAC,KAAK,CAAC;YACjBC,cAAc,CAAC,KAAK,CAAC;YACrBI,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7CJ,UAAU,CAAC,KAAK,CAAC;YACjB,OAAO,CAAC;UACV,CAAC,MAAM;YACLG,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UACrE;QACF,CAAC,CAAC,OAAOU,SAAc,EAAE;UAAA,IAAAC,mBAAA;UACvBZ,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE,EAAAW,mBAAA,GAAAD,SAAS,CAACT,QAAQ,cAAAU,mBAAA,uBAAlBA,mBAAA,CAAoBJ,MAAM,KAAIG,SAAS,CAACF,OAAO,CAAC;QACvH;;QAEA;QACAT,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QACxDT,OAAO,CAAC,IAAI,CAAC;QACbC,QAAQ,CAAC,IAAI,CAAC;QACdC,kBAAkB,CAAC,KAAK,CAAC;QACzBC,UAAU,CAAC,KAAK,CAAC;QACjBC,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdgB,OAAO,CAAChB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACAQ,OAAO,CAAC,IAAI,CAAC;QACbC,QAAQ,CAAC,IAAI,CAAC;QACdC,kBAAkB,CAAC,KAAK,CAAC;QACzBC,UAAU,CAAC,KAAK,CAAC;QACjBC,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,SAAS;QACRC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDE,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMd,KAAK,GAAG,MAAAA,CAAO4B,KAAa,EAAEC,QAAgB,KAAK;IACvD,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBT,UAAU,CAAC,CAAC;MAEZ,MAAMc,QAAQ,GAAG,MAAM7B,gBAAgB,CAACY,KAAK,CAAC4B,KAAK,EAAEC,QAAQ,CAAC;MAE9D,IAAIZ,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,CAAC3B,IAAI,EAAE;QAC1Cc,OAAO,CAACU,QAAQ,CAACG,IAAI,CAAC3B,IAAI,CAAC;QAC3Be,QAAQ,CAAC,IAAI,CAAC;QACdC,kBAAkB,CAAC,IAAI,CAAC;QACxBC,UAAU,CAAC,KAAK,CAAC;QACjBC,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM;QACL,MAAM,IAAImB,KAAK,CAACb,QAAQ,CAACO,OAAO,IAAI,cAAc,CAAC;MACrD;IACF,CAAC,CAAC,OAAOzB,KAAU,EAAE;MAAA,IAAAgC,eAAA,EAAAC,oBAAA;MACnBnB,QAAQ,CAAC,EAAAkB,eAAA,GAAAhC,KAAK,CAACkB,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBX,IAAI,cAAAY,oBAAA,uBAApBA,oBAAA,CAAsBR,OAAO,KAAIzB,KAAK,CAACyB,OAAO,IAAI,cAAc,CAAC;MAC1EjB,OAAO,CAAC,IAAI,CAAC;MACbC,QAAQ,CAAC,IAAI,CAAC;MACdC,kBAAkB,CAAC,KAAK,CAAC;MACzBC,UAAU,CAAC,KAAK,CAAC;MACjBC,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,SAAS;MACRC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMX,UAAU,GAAG,MAAAA,CAAO2B,KAAa,EAAEC,QAAgB,KAAK;IAC5D,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBT,UAAU,CAAC,CAAC;MAEZY,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;QAAEY;MAAM,CAAC,CAAC;MAClE,MAAMX,QAAQ,GAAG,MAAM7B,gBAAgB,CAACa,UAAU,CAAC2B,KAAK,EAAEC,QAAQ,CAAC;MACnEd,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAAC;MAE9C,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,CAAC1B,KAAK,EAAE;QAC3CqB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,QAAQ,CAACG,IAAI,CAAC1B,KAAK,CAAC;;QAE3D;QACAc,QAAQ,CAACS,QAAQ,CAACG,IAAI,CAAC1B,KAAK,CAAC;QAC7Ba,OAAO,CAAC,IAAI,CAAC;QACbE,kBAAkB,CAAC,IAAI,CAAC;QACxBC,UAAU,CAAC,IAAI,CAAC;QAChBC,cAAc,CAACM,QAAQ,CAACG,IAAI,CAAC1B,KAAK,CAACG,WAAW,CAAC;QAE/CkB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC5D,CAAC,MAAM;QACL,MAAM,IAAIc,KAAK,CAACb,QAAQ,CAACO,OAAO,IAAI,oBAAoB,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOzB,KAAU,EAAE;MAAA,IAAAkC,gBAAA,EAAAC,qBAAA;MACnBnB,OAAO,CAAChB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1Cc,QAAQ,CAAC,EAAAoB,gBAAA,GAAAlC,KAAK,CAACkB,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBb,IAAI,cAAAc,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAIzB,KAAK,CAACyB,OAAO,IAAI,oBAAoB,CAAC;MAChFjB,OAAO,CAAC,IAAI,CAAC;MACbC,QAAQ,CAAC,IAAI,CAAC;MACdC,kBAAkB,CAAC,KAAK,CAAC;MACzBC,UAAU,CAAC,KAAK,CAAC;MACjBC,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,SAAS;MACRC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMV,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACFU,UAAU,CAAC,IAAI,CAAC;MAChBT,UAAU,CAAC,CAAC;MAEZ,MAAMf,gBAAgB,CAACc,MAAM,CAAC,CAAC;;MAE/B;MACAb,oBAAoB,CAAC,CAAC;MAEtBkB,OAAO,CAAC,IAAI,CAAC;MACbC,QAAQ,CAAC,IAAI,CAAC;MACdC,kBAAkB,CAAC,KAAK,CAAC;MACzBC,UAAU,CAAC,KAAK,CAAC;MACjBC,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAAoC,gBAAA,EAAAC,qBAAA;MACnBvB,QAAQ,CAAC,EAAAsB,gBAAA,GAAApC,KAAK,CAACkB,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBZ,OAAO,KAAIzB,KAAK,CAACyB,OAAO,IAAI,eAAe,CAAC;IAC7E,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACErB,OAAA,CAACC,iBAAiB,CAAC6C,QAAQ;IACzBC,KAAK,EAAE;MACL7C,IAAI;MACJC,KAAK;MACLC,eAAe;MACfC,OAAO;MACPC,WAAW;MACXC,OAAO;MACPC,KAAK;MACLC,KAAK;MACLC,UAAU;MACVC,MAAM;MACNC;IACF,CAAE;IAAAE,QAAA,EAEDA;EAAQ;IAAAkC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACiB,CAAC;AAEjC,CAAC;;AAED;AAAApC,EAAA,CA1MaF,kBAAqD;AAAAuC,EAAA,GAArDvC,kBAAqD;AA2MlE,OAAO,MAAMwC,aAAa,GAAGA,CAAA;EAAAC,GAAA;EAAA,OAAM3D,UAAU,CAACM,iBAAiB,CAAC;AAAA;AAACqD,GAAA,CAApDD,aAAa;AAE1B,eAAepD,iBAAiB;AAAC,IAAAmD,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}