{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Spin, Typography, Alert } from 'antd';\nimport { BarChart, Bar, PieChart, Pie, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell } from 'recharts';\nimport { useAdminApi } from '../../hooks/useAdminApi';\nimport authService from '../../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\n\n// Define chart colors\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];\nconst AnalyticsDashboard = () => {\n  _s();\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const api = useAdminApi();\n  const fetchAnalyticsData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      try {\n        // Use the new auth service which handles HTTP-only cookies\n        const response = await authService.request('GET', '/admin/analytics');\n\n        // Check if we got valid data\n        if (!response.data || Object.keys(response.data).length === 0) {\n          setError('No analytics data available. This could be because there is no data in the system yet.');\n          setAnalyticsData(null);\n        } else {\n          setAnalyticsData(response.data);\n          setError(null);\n        }\n      } catch (apiError) {\n        console.error('Error fetching analytics data from API:', apiError);\n\n        // Check if it's an authentication error\n        if (apiError.response && apiError.response.status === 401) {\n          setError('Authentication error. Please log in again to view analytics.');\n          return;\n        }\n\n        // For development or when API fails, use mock data\n        if (process.env.NODE_ENV === 'development' || apiError) {\n          // Mock data for testing\n          const mockData = {\n            scholarshipsByStatus: [{\n              name: 'Open',\n              value: 12\n            }, {\n              name: 'Closed',\n              value: 8\n            }],\n            scholarshipsByLevel: [{\n              name: 'Undergraduate',\n              value: 8\n            }, {\n              name: 'Graduate',\n              value: 7\n            }, {\n              name: 'PhD',\n              value: 3\n            }, {\n              name: 'Postdoctoral',\n              value: 2\n            }],\n            scholarshipsByCountry: [{\n              name: 'United States',\n              value: 10\n            }, {\n              name: 'Canada',\n              value: 5\n            }, {\n              name: 'United Kingdom',\n              value: 3\n            }, {\n              name: 'Australia',\n              value: 2\n            }],\n            scholarshipsTimeSeries: [{\n              month: '2023-01',\n              count: 2\n            }, {\n              month: '2023-02',\n              count: 3\n            }, {\n              month: '2023-03',\n              count: 5\n            }, {\n              month: '2023-04',\n              count: 4\n            }, {\n              month: '2023-05',\n              count: 6\n            }, {\n              month: '2023-06',\n              count: 8\n            }],\n            messagesTimeSeries: [{\n              month: '2023-01',\n              count: 5\n            }, {\n              month: '2023-02',\n              count: 7\n            }, {\n              month: '2023-03',\n              count: 10\n            }, {\n              month: '2023-04',\n              count: 8\n            }, {\n              month: '2023-05',\n              count: 12\n            }, {\n              month: '2023-06',\n              count: 15\n            }],\n            subscribersTimeSeries: [{\n              month: '2023-01',\n              count: 10\n            }, {\n              month: '2023-02',\n              count: 15\n            }, {\n              month: '2023-03',\n              count: 20\n            }, {\n              month: '2023-04',\n              count: 25\n            }, {\n              month: '2023-05',\n              count: 30\n            }, {\n              month: '2023-06',\n              count: 35\n            }]\n          };\n          setAnalyticsData(mockData);\n          console.log('Using mock data for analytics');\n        }\n      }\n    } catch (err) {\n      console.error('Error in fetchAnalyticsData:', err);\n      setError('Failed to load analytics data. Please try again later.');\n      setAnalyticsData(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchAnalyticsData();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // Format month for display\n  const formatMonth = month => {\n    const date = new Date(month + '-01');\n    return date.toLocaleDateString('en-US', {\n      month: 'short',\n      year: 'numeric'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\",\n        tip: \"Loading analytics data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Error\",\n      description: error,\n      type: \"error\",\n      showIcon: true,\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this);\n  }\n  if (!analyticsData) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"No Data\",\n      description: \"No analytics data available.\",\n      type: \"info\",\n      showIcon: true,\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"analytics-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 3,\n        className: \"mb-2\",\n        children: \"Analytics Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        className: \"text-gray-500\",\n        children: \"Visualize and analyze your scholarship data and user engagement metrics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center p-8\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      message: error,\n      type: \"error\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this) : analyticsData ? /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"Scholarships by Status\",\n          className: \"h-full\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: analyticsData.scholarshipsByStatus,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: true,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                nameKey: \"name\",\n                label: ({\n                  name,\n                  percent\n                }) => `${name}: ${(percent * 100).toFixed(0)}%`,\n                children: analyticsData.scholarshipsByStatus.map((_, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: COLORS[index % COLORS.length]\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                formatter: value => [`${value} scholarships`, 'Count']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"Scholarships by Level\",\n          className: \"h-full\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: analyticsData.scholarshipsByLevel,\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                formatter: value => [`${value} scholarships`, 'Count']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"value\",\n                name: \"Count\",\n                fill: \"#8884d8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"Scholarships by Country\",\n          className: \"h-full\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: analyticsData.scholarshipsByCountry,\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              layout: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                type: \"number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                dataKey: \"name\",\n                type: \"category\",\n                width: 150\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                formatter: value => [`${value} scholarships`, 'Count']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"value\",\n                name: \"Count\",\n                fill: \"#82ca9d\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"Activity Over Time\",\n          className: \"h-full\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 400,\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: analyticsData.scholarshipsTimeSeries,\n              margin: {\n                top: 5,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"month\",\n                tickFormatter: formatMonth\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                labelFormatter: formatMonth,\n                formatter: value => [`${value}`, 'Count']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"count\",\n                name: \"Scholarships\",\n                stroke: \"#8884d8\",\n                activeDot: {\n                  r: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 9\n    }, this) : null]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalyticsDashboard, \"x7iIntxh3Ww3hppGsC2Hv81SY/4=\", false, function () {\n  return [useAdminApi];\n});\n_c = AnalyticsDashboard;\nexport default AnalyticsDashboard;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Spin", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bar", "<PERSON><PERSON><PERSON>", "Pie", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "Cell", "useAdminApi", "authService", "jsxDEV", "_jsxDEV", "Title", "Text", "COLORS", "AnalyticsDashboard", "_s", "analyticsData", "setAnalyticsData", "loading", "setLoading", "error", "setError", "api", "fetchAnalyticsData", "response", "request", "data", "Object", "keys", "length", "apiError", "console", "status", "process", "env", "NODE_ENV", "mockData", "scholarshipsByStatus", "name", "value", "scholarshipsByLevel", "scholarshipsByCountry", "scholarshipsTimeSeries", "month", "count", "messagesTimeSeries", "subscribersTimeSeries", "log", "err", "formatMonth", "date", "Date", "toLocaleDateString", "year", "className", "children", "size", "tip", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "description", "type", "showIcon", "level", "gutter", "xs", "lg", "title", "width", "height", "cx", "cy", "labelLine", "outerRadius", "fill", "dataKey", "<PERSON><PERSON><PERSON>", "label", "percent", "toFixed", "map", "_", "index", "formatter", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "layout", "tick<PERSON><PERSON><PERSON><PERSON>", "labelFormatter", "stroke", "activeDot", "r", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>, Row, Col, Spin, Typography, Alert } from 'antd';\nimport {\n  <PERSON><PERSON><PERSON>, <PERSON>,\n  <PERSON><PERSON><PERSON>, <PERSON>,\n  <PERSON><PERSON><PERSON>, Line,\n  XAxis, YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  Cell\n} from 'recharts';\nimport { useAdminApi } from '../../hooks/useAdminApi';\nimport authService from '../../services/authService';\n\nconst { Title, Text } = Typography;\n\n// Define chart colors\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];\n\ninterface AnalyticsData {\n  scholarshipsByStatus: { name: string; value: number }[];\n  scholarshipsByLevel: { name: string; value: number }[];\n  scholarshipsByCountry: { name: string; value: number }[];\n  scholarshipsTimeSeries: { month: string; count: number }[];\n  messagesTimeSeries: { month: string; count: number }[];\n  subscribersTimeSeries: { month: string; count: number }[];\n  userEngagement?: { date: string; visits: number; actions: number }[];\n  applicationConversion?: { stage: string; count: number }[];\n}\n\nconst AnalyticsDashboard: React.FC = () => {\n  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const api = useAdminApi();\n\n  const fetchAnalyticsData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      try {\n        // Use the new auth service which handles HTTP-only cookies\n        const response = await authService.request('GET', '/admin/analytics') as { data: AnalyticsData };\n\n        // Check if we got valid data\n        if (!response.data || Object.keys(response.data).length === 0) {\n          setError('No analytics data available. This could be because there is no data in the system yet.');\n          setAnalyticsData(null);\n        } else {\n          setAnalyticsData(response.data);\n          setError(null);\n        }\n      } catch (apiError: any) {\n        console.error('Error fetching analytics data from API:', apiError);\n\n        // Check if it's an authentication error\n        if (apiError.response && apiError.response.status === 401) {\n          setError('Authentication error. Please log in again to view analytics.');\n          return;\n        }\n\n        // For development or when API fails, use mock data\n        if (process.env.NODE_ENV === 'development' || apiError) {\n          // Mock data for testing\n          const mockData: AnalyticsData = {\n            scholarshipsByStatus: [\n              { name: 'Open', value: 12 },\n              { name: 'Closed', value: 8 }\n            ],\n            scholarshipsByLevel: [\n              { name: 'Undergraduate', value: 8 },\n              { name: 'Graduate', value: 7 },\n              { name: 'PhD', value: 3 },\n              { name: 'Postdoctoral', value: 2 }\n            ],\n            scholarshipsByCountry: [\n              { name: 'United States', value: 10 },\n              { name: 'Canada', value: 5 },\n              { name: 'United Kingdom', value: 3 },\n              { name: 'Australia', value: 2 }\n            ],\n            scholarshipsTimeSeries: [\n              { month: '2023-01', count: 2 },\n              { month: '2023-02', count: 3 },\n              { month: '2023-03', count: 5 },\n              { month: '2023-04', count: 4 },\n              { month: '2023-05', count: 6 },\n              { month: '2023-06', count: 8 }\n            ],\n            messagesTimeSeries: [\n              { month: '2023-01', count: 5 },\n              { month: '2023-02', count: 7 },\n              { month: '2023-03', count: 10 },\n              { month: '2023-04', count: 8 },\n              { month: '2023-05', count: 12 },\n              { month: '2023-06', count: 15 }\n            ],\n            subscribersTimeSeries: [\n              { month: '2023-01', count: 10 },\n              { month: '2023-02', count: 15 },\n              { month: '2023-03', count: 20 },\n              { month: '2023-04', count: 25 },\n              { month: '2023-05', count: 30 },\n              { month: '2023-06', count: 35 }\n            ]\n          };\n\n          setAnalyticsData(mockData);\n          console.log('Using mock data for analytics');\n        }\n      }\n    } catch (err: any) {\n      console.error('Error in fetchAnalyticsData:', err);\n      setError('Failed to load analytics data. Please try again later.');\n      setAnalyticsData(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchAnalyticsData();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // Format month for display\n  const formatMonth = (month: string) => {\n    const date = new Date(month + '-01');\n    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <Spin size=\"large\" tip=\"Loading analytics data...\" />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Alert\n        message=\"Error\"\n        description={error}\n        type=\"error\"\n        showIcon\n        className=\"mb-6\"\n      />\n    );\n  }\n\n  if (!analyticsData) {\n    return (\n      <Alert\n        message=\"No Data\"\n        description=\"No analytics data available.\"\n        type=\"info\"\n        showIcon\n        className=\"mb-6\"\n      />\n    );\n  }\n\n  return (\n    <div className=\"analytics-dashboard\">\n      <div className=\"mb-6\">\n        <Title level={3} className=\"mb-2\">Analytics Dashboard</Title>\n        <Text className=\"text-gray-500\">\n          Visualize and analyze your scholarship data and user engagement metrics\n        </Text>\n      </div>\n\n      {loading ? (\n        <div className=\"flex justify-center items-center p-8\">\n          <Spin size=\"large\" />\n        </div>\n      ) : error ? (\n        <Alert message={error} type=\"error\" />\n      ) : analyticsData ? (\n        <Row gutter={[16, 16]}>\n          {/* Scholarships by Status */}\n          <Col xs={24} lg={12}>\n            <Card title=\"Scholarships by Status\" className=\"h-full\">\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={analyticsData.scholarshipsByStatus}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    labelLine={true}\n                    outerRadius={80}\n                    fill=\"#8884d8\"\n                    dataKey=\"value\"\n                    nameKey=\"name\"\n                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}\n                  >\n                    {analyticsData.scholarshipsByStatus.map((_, index) => (\n                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                    ))}\n                  </Pie>\n                  <Tooltip formatter={(value) => [`${value} scholarships`, 'Count']} />\n                  <Legend />\n                </PieChart>\n              </ResponsiveContainer>\n            </Card>\n          </Col>\n\n          {/* Scholarships by Level */}\n          <Col xs={24} lg={12}>\n            <Card title=\"Scholarships by Level\" className=\"h-full\">\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart\n                  data={analyticsData.scholarshipsByLevel}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"name\" />\n                  <YAxis />\n                  <Tooltip formatter={(value) => [`${value} scholarships`, 'Count']} />\n                  <Legend />\n                  <Bar dataKey=\"value\" name=\"Count\" fill=\"#8884d8\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </Card>\n          </Col>\n\n          {/* Scholarships by Country */}\n          <Col xs={24}>\n            <Card title=\"Scholarships by Country\" className=\"h-full\">\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart\n                  data={analyticsData.scholarshipsByCountry}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                  layout=\"vertical\"\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis type=\"number\" />\n                  <YAxis dataKey=\"name\" type=\"category\" width={150} />\n                  <Tooltip formatter={(value) => [`${value} scholarships`, 'Count']} />\n                  <Legend />\n                  <Bar dataKey=\"value\" name=\"Count\" fill=\"#82ca9d\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </Card>\n          </Col>\n\n          {/* Time Series Data */}\n          <Col xs={24}>\n            <Card title=\"Activity Over Time\" className=\"h-full\">\n              <ResponsiveContainer width=\"100%\" height={400}>\n                <LineChart\n                  data={analyticsData.scholarshipsTimeSeries}\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n                >\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis\n                    dataKey=\"month\"\n                    tickFormatter={formatMonth}\n                  />\n                  <YAxis />\n                  <Tooltip\n                    labelFormatter={formatMonth}\n                    formatter={(value) => [`${value}`, 'Count']}\n                  />\n                  <Legend />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"count\"\n                    name=\"Scholarships\"\n                    stroke=\"#8884d8\"\n                    activeDot={{ r: 8 }}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </Card>\n          </Col>\n        </Row>\n      ) : null}\n    </div>\n  );\n};\n\nexport default AnalyticsDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,KAAK,QAAQ,MAAM;AAC9D,SACEC,QAAQ,EAAEC,GAAG,EACbC,QAAQ,EAAEC,GAAG,EACbC,SAAS,EAAEC,IAAI,EACfC,KAAK,EAAEC,KAAK,EACZC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,IAAI,QACC,UAAU;AACjB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;;AAElC;AACA,MAAMqB,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AAajF,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAuB,IAAI,CAAC;EAC9E,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAMoC,GAAG,GAAGf,WAAW,CAAC,CAAC;EAEzB,MAAMgB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFJ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF;QACA,MAAMG,QAAQ,GAAG,MAAMhB,WAAW,CAACiB,OAAO,CAAC,KAAK,EAAE,kBAAkB,CAA4B;;QAEhG;QACA,IAAI,CAACD,QAAQ,CAACE,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,IAAI,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;UAC7DR,QAAQ,CAAC,wFAAwF,CAAC;UAClGJ,gBAAgB,CAAC,IAAI,CAAC;QACxB,CAAC,MAAM;UACLA,gBAAgB,CAACO,QAAQ,CAACE,IAAI,CAAC;UAC/BL,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF,CAAC,CAAC,OAAOS,QAAa,EAAE;QACtBC,OAAO,CAACX,KAAK,CAAC,yCAAyC,EAAEU,QAAQ,CAAC;;QAElE;QACA,IAAIA,QAAQ,CAACN,QAAQ,IAAIM,QAAQ,CAACN,QAAQ,CAACQ,MAAM,KAAK,GAAG,EAAE;UACzDX,QAAQ,CAAC,8DAA8D,CAAC;UACxE;QACF;;QAEA;QACA,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIL,QAAQ,EAAE;UACtD;UACA,MAAMM,QAAuB,GAAG;YAC9BC,oBAAoB,EAAE,CACpB;cAAEC,IAAI,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAG,CAAC,EAC3B;cAAED,IAAI,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAE,CAAC,CAC7B;YACDC,mBAAmB,EAAE,CACnB;cAAEF,IAAI,EAAE,eAAe;cAAEC,KAAK,EAAE;YAAE,CAAC,EACnC;cAAED,IAAI,EAAE,UAAU;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC9B;cAAED,IAAI,EAAE,KAAK;cAAEC,KAAK,EAAE;YAAE,CAAC,EACzB;cAAED,IAAI,EAAE,cAAc;cAAEC,KAAK,EAAE;YAAE,CAAC,CACnC;YACDE,qBAAqB,EAAE,CACrB;cAAEH,IAAI,EAAE,eAAe;cAAEC,KAAK,EAAE;YAAG,CAAC,EACpC;cAAED,IAAI,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC5B;cAAED,IAAI,EAAE,gBAAgB;cAAEC,KAAK,EAAE;YAAE,CAAC,EACpC;cAAED,IAAI,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAE,CAAC,CAChC;YACDG,sBAAsB,EAAE,CACtB;cAAEC,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC9B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC9B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC9B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC9B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC9B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAE,CAAC,CAC/B;YACDC,kBAAkB,EAAE,CAClB;cAAEF,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC9B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC9B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAG,CAAC,EAC/B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAE,CAAC,EAC9B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAG,CAAC,EAC/B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAG,CAAC,CAChC;YACDE,qBAAqB,EAAE,CACrB;cAAEH,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAG,CAAC,EAC/B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAG,CAAC,EAC/B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAG,CAAC,EAC/B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAG,CAAC,EAC/B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAG,CAAC,EAC/B;cAAED,KAAK,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAG,CAAC;UAEnC,CAAC;UAED3B,gBAAgB,CAACmB,QAAQ,CAAC;UAC1BL,OAAO,CAACgB,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBjB,OAAO,CAACX,KAAK,CAAC,8BAA8B,EAAE4B,GAAG,CAAC;MAClD3B,QAAQ,CAAC,wDAAwD,CAAC;MAClEJ,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAhC,SAAS,CAAC,MAAM;IACdoC,kBAAkB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0B,WAAW,GAAIN,KAAa,IAAK;IACrC,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAACR,KAAK,GAAG,KAAK,CAAC;IACpC,OAAOO,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAAET,KAAK,EAAE,OAAO;MAAEU,IAAI,EAAE;IAAU,CAAC,CAAC;EAC9E,CAAC;EAED,IAAInC,OAAO,EAAE;IACX,oBACER,OAAA;MAAK4C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD7C,OAAA,CAACnB,IAAI;QAACiE,IAAI,EAAC,OAAO;QAACC,GAAG,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEV;EAEA,IAAIzC,KAAK,EAAE;IACT,oBACEV,OAAA,CAACjB,KAAK;MACJqE,OAAO,EAAC,OAAO;MACfC,WAAW,EAAE3C,KAAM;MACnB4C,IAAI,EAAC,OAAO;MACZC,QAAQ;MACRX,SAAS,EAAC;IAAM;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEN;EAEA,IAAI,CAAC7C,aAAa,EAAE;IAClB,oBACEN,OAAA,CAACjB,KAAK;MACJqE,OAAO,EAAC,SAAS;MACjBC,WAAW,EAAC,8BAA8B;MAC1CC,IAAI,EAAC,MAAM;MACXC,QAAQ;MACRX,SAAS,EAAC;IAAM;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEN;EAEA,oBACEnD,OAAA;IAAK4C,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClC7C,OAAA;MAAK4C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB7C,OAAA,CAACC,KAAK;QAACuD,KAAK,EAAE,CAAE;QAACZ,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC7DnD,OAAA,CAACE,IAAI;QAAC0C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAEhC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAEL3C,OAAO,gBACNR,OAAA;MAAK4C,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnD7C,OAAA,CAACnB,IAAI;QAACiE,IAAI,EAAC;MAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,GACJzC,KAAK,gBACPV,OAAA,CAACjB,KAAK;MAACqE,OAAO,EAAE1C,KAAM;MAAC4C,IAAI,EAAC;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACpC7C,aAAa,gBACfN,OAAA,CAACrB,GAAG;MAAC8E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAZ,QAAA,gBAEpB7C,OAAA,CAACpB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAd,QAAA,eAClB7C,OAAA,CAACtB,IAAI;UAACkF,KAAK,EAAC,wBAAwB;UAAChB,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACrD7C,OAAA,CAACL,mBAAmB;YAACkE,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAAjB,QAAA,eAC5C7C,OAAA,CAACd,QAAQ;cAAA2D,QAAA,gBACP7C,OAAA,CAACb,GAAG;gBACF6B,IAAI,EAAEV,aAAa,CAACqB,oBAAqB;gBACzCoC,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,SAAS,EAAE,IAAK;gBAChBC,WAAW,EAAE,EAAG;gBAChBC,IAAI,EAAC,SAAS;gBACdC,OAAO,EAAC,OAAO;gBACfC,OAAO,EAAC,MAAM;gBACdC,KAAK,EAAEA,CAAC;kBAAE1C,IAAI;kBAAE2C;gBAAQ,CAAC,KAAK,GAAG3C,IAAI,KAAK,CAAC2C,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;gBAAA3B,QAAA,EAEvEvC,aAAa,CAACqB,oBAAoB,CAAC8C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAC/C3E,OAAA,CAACJ,IAAI;kBAAuBuE,IAAI,EAAEhE,MAAM,CAACwE,KAAK,GAAGxE,MAAM,CAACgB,MAAM;gBAAE,GAArD,QAAQwD,KAAK,EAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAwC,CACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnD,OAAA,CAACP,OAAO;gBAACmF,SAAS,EAAG/C,KAAK,IAAK,CAAC,GAAGA,KAAK,eAAe,EAAE,OAAO;cAAE;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrEnD,OAAA,CAACN,MAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNnD,OAAA,CAACpB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAd,QAAA,eAClB7C,OAAA,CAACtB,IAAI;UAACkF,KAAK,EAAC,uBAAuB;UAAChB,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACpD7C,OAAA,CAACL,mBAAmB;YAACkE,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAAjB,QAAA,eAC5C7C,OAAA,CAAChB,QAAQ;cACPgC,IAAI,EAAEV,aAAa,CAACwB,mBAAoB;cACxC+C,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAApC,QAAA,gBAEnD7C,OAAA,CAACR,aAAa;gBAAC0F,eAAe,EAAC;cAAK;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACV,KAAK;gBAAC8E,OAAO,EAAC;cAAM;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxBnD,OAAA,CAACT,KAAK;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTnD,OAAA,CAACP,OAAO;gBAACmF,SAAS,EAAG/C,KAAK,IAAK,CAAC,GAAGA,KAAK,eAAe,EAAE,OAAO;cAAE;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrEnD,OAAA,CAACN,MAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVnD,OAAA,CAACf,GAAG;gBAACmF,OAAO,EAAC,OAAO;gBAACxC,IAAI,EAAC,OAAO;gBAACuC,IAAI,EAAC;cAAS;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNnD,OAAA,CAACpB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAAAb,QAAA,eACV7C,OAAA,CAACtB,IAAI;UAACkF,KAAK,EAAC,yBAAyB;UAAChB,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACtD7C,OAAA,CAACL,mBAAmB;YAACkE,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAAjB,QAAA,eAC5C7C,OAAA,CAAChB,QAAQ;cACPgC,IAAI,EAAEV,aAAa,CAACyB,qBAAsB;cAC1C8C,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cACnDE,MAAM,EAAC,UAAU;cAAAtC,QAAA,gBAEjB7C,OAAA,CAACR,aAAa;gBAAC0F,eAAe,EAAC;cAAK;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACV,KAAK;gBAACgE,IAAI,EAAC;cAAQ;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvBnD,OAAA,CAACT,KAAK;gBAAC6E,OAAO,EAAC,MAAM;gBAACd,IAAI,EAAC,UAAU;gBAACO,KAAK,EAAE;cAAI;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpDnD,OAAA,CAACP,OAAO;gBAACmF,SAAS,EAAG/C,KAAK,IAAK,CAAC,GAAGA,KAAK,eAAe,EAAE,OAAO;cAAE;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrEnD,OAAA,CAACN,MAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVnD,OAAA,CAACf,GAAG;gBAACmF,OAAO,EAAC,OAAO;gBAACxC,IAAI,EAAC,OAAO;gBAACuC,IAAI,EAAC;cAAS;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNnD,OAAA,CAACpB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAAAb,QAAA,eACV7C,OAAA,CAACtB,IAAI;UAACkF,KAAK,EAAC,oBAAoB;UAAChB,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACjD7C,OAAA,CAACL,mBAAmB;YAACkE,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAAjB,QAAA,eAC5C7C,OAAA,CAACZ,SAAS;cACR4B,IAAI,EAAEV,aAAa,CAAC0B,sBAAuB;cAC3C6C,MAAM,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAApC,QAAA,gBAEnD7C,OAAA,CAACR,aAAa;gBAAC0F,eAAe,EAAC;cAAK;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACV,KAAK;gBACJ8E,OAAO,EAAC,OAAO;gBACfgB,aAAa,EAAE7C;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFnD,OAAA,CAACT,KAAK;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTnD,OAAA,CAACP,OAAO;gBACN4F,cAAc,EAAE9C,WAAY;gBAC5BqC,SAAS,EAAG/C,KAAK,IAAK,CAAC,GAAGA,KAAK,EAAE,EAAE,OAAO;cAAE;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACFnD,OAAA,CAACN,MAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVnD,OAAA,CAACX,IAAI;gBACHiE,IAAI,EAAC,UAAU;gBACfc,OAAO,EAAC,OAAO;gBACfxC,IAAI,EAAC,cAAc;gBACnB0D,MAAM,EAAC,SAAS;gBAChBC,SAAS,EAAE;kBAAEC,CAAC,EAAE;gBAAE;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ,IAAI;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA7PID,kBAA4B;EAAA,QAKpBP,WAAW;AAAA;AAAA4F,EAAA,GALnBrF,kBAA4B;AA+PlC,eAAeA,kBAAkB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}