{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Tag, Typography, Statistic, Row, Col, DatePicker, Button, Space, Alert, Spin } from 'antd';\nimport { SecurityScanOutlined, WarningOutlined, CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { format } from 'date-fns';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\n\n// Define security event types\nvar SecurityEventType = /*#__PURE__*/function (SecurityEventType) {\n  SecurityEventType[\"LOGIN_SUCCESS\"] = \"LOGIN_SUCCESS\";\n  SecurityEventType[\"LOGIN_FAILURE\"] = \"LOGIN_FAILURE\";\n  SecurityEventType[\"LOGOUT\"] = \"LOGOUT\";\n  SecurityEventType[\"PASSWORD_RESET\"] = \"PASSWORD_RESET\";\n  SecurityEventType[\"ACCOUNT_LOCKED\"] = \"ACCOUNT_LOCKED\";\n  SecurityEventType[\"SUSPICIOUS_ACTIVITY\"] = \"SUSPICIOUS_ACTIVITY\";\n  SecurityEventType[\"TWO_FACTOR_SUCCESS\"] = \"TWO_FACTOR_SUCCESS\";\n  SecurityEventType[\"TWO_FACTOR_FAILURE\"] = \"TWO_FACTOR_FAILURE\";\n  return SecurityEventType;\n}(SecurityEventType || {}); // Define security event interface\n// Define security stats interface\nconst SecurityDashboard = () => {\n  _s();\n  const {\n    admin\n  } = useAuth();\n  const isMainAdmin = (admin === null || admin === void 0 ? void 0 : admin.isMainAdmin) || false;\n  const [loading, setLoading] = useState(true);\n  const [events, setEvents] = useState([]);\n  const [stats, setStats] = useState({\n    totalEvents: 0,\n    successfulLogins: 0,\n    failedLogins: 0,\n    suspiciousActivities: 0,\n    accountLockouts: 0,\n    twoFactorSuccesses: 0,\n    twoFactorFailures: 0\n  });\n  const [dateRange, setDateRange] = useState(null);\n  const [error, setError] = useState(null);\n\n  // Fetch security events\n  const fetchSecurityEvents = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Prepare date range parameters\n      const params = {};\n      if (dateRange) {\n        params.startDate = dateRange[0].toISOString();\n        params.endDate = dateRange[1].toISOString();\n      }\n\n      // Fetch events from API\n      const response = await secureApiService.get('/admin/security/events', {\n        params\n      });\n      if (response.success && response.data) {\n        setEvents(response.data.events);\n        setStats(response.data.stats);\n      } else {\n        setError(response.message || 'Failed to fetch security events');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message || 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchSecurityEvents();\n  }, []);\n\n  // Handle date range change\n  const handleDateRangeChange = dates => {\n    if (dates) {\n      setDateRange([dates[0].toDate(), dates[1].toDate()]);\n    } else {\n      setDateRange(null);\n    }\n  };\n\n  // Handle refresh button click\n  const handleRefresh = () => {\n    fetchSecurityEvents();\n  };\n\n  // Get tag color based on event type\n  const getEventTypeTag = eventType => {\n    switch (eventType) {\n      case SecurityEventType.LOGIN_SUCCESS:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 43\n          }, this),\n          children: \"Login Success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 16\n        }, this);\n      case SecurityEventType.LOGIN_FAILURE:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 41\n          }, this),\n          children: \"Login Failure\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 16\n        }, this);\n      case SecurityEventType.LOGOUT:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 16\n        }, this);\n      case SecurityEventType.PASSWORD_RESET:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          children: \"Password Reset\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 16\n        }, this);\n      case SecurityEventType.ACCOUNT_LOCKED:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 41\n          }, this),\n          children: \"Account Locked\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 16\n        }, this);\n      case SecurityEventType.SUSPICIOUS_ACTIVITY:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 43\n          }, this),\n          children: \"Suspicious Activity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 16\n        }, this);\n      case SecurityEventType.TWO_FACTOR_SUCCESS:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          children: \"2FA Success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 16\n        }, this);\n      case SecurityEventType.TWO_FACTOR_FAILURE:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          children: \"2FA Failure\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          children: \"Unknown\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Get severity tag\n  const getSeverityTag = severity => {\n    switch (severity) {\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          children: \"Info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 16\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"orange\",\n          children: \"Warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"red\",\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 16\n        }, this);\n      case 'critical':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"volcano\",\n          icon: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 43\n          }, this),\n          children: \"Critical\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          children: \"Unknown\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Table columns\n  const columns = [{\n    title: 'Time',\n    dataIndex: 'timestamp',\n    key: 'timestamp',\n    render: text => format(new Date(text), 'yyyy-MM-dd HH:mm:ss'),\n    sorter: (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()\n  }, {\n    title: 'Event',\n    dataIndex: 'eventType',\n    key: 'eventType',\n    render: text => getEventTypeTag(text),\n    filters: Object.values(SecurityEventType).map(type => ({\n      text: type,\n      value: type\n    })),\n    onFilter: (value, record) => record.eventType === value.toString()\n  }, {\n    title: 'User',\n    dataIndex: 'email',\n    key: 'email',\n    render: (text, record) => text || 'N/A'\n  }, {\n    title: 'IP Address',\n    dataIndex: 'ip',\n    key: 'ip'\n  }, {\n    title: 'Severity',\n    dataIndex: 'severity',\n    key: 'severity',\n    render: text => getSeverityTag(text),\n    filters: [{\n      text: 'Info',\n      value: 'info'\n    }, {\n      text: 'Warning',\n      value: 'warning'\n    }, {\n      text: 'Error',\n      value: 'error'\n    }, {\n      text: 'Critical',\n      value: 'critical'\n    }],\n    onFilter: (value, record) => record.severity === value.toString()\n  }, {\n    title: 'Message',\n    dataIndex: 'message',\n    key: 'message'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(SecurityScanOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), \" Security Dashboard\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      className: \"mb-6 block text-gray-600\",\n      children: \"Monitor security events and detect potential threats to your system.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), !isMainAdmin && /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Limited Access\",\n      description: \"You have limited access to security information. Contact the main administrator for full access.\",\n      type: \"warning\",\n      showIcon: true,\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-6 shadow-md\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Total Events\",\n            value: stats.totalEvents,\n            prefix: /*#__PURE__*/_jsxDEV(SecurityScanOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Successful Logins\",\n            value: stats.successfulLogins,\n            valueStyle: {\n              color: '#3f8600'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Failed Logins\",\n            value: stats.failedLogins,\n            valueStyle: {\n              color: '#cf1322'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Suspicious Activities\",\n            value: stats.suspiciousActivities,\n            valueStyle: {\n              color: '#faad14'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-6 shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        direction: \"horizontal\",\n        size: \"middle\",\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(RangePicker, {\n          onChange: handleDateRangeChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 19\n          }, this),\n          onClick: handleRefresh,\n          loading: loading,\n          children: \"Refresh\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Error\",\n        description: error,\n        type: \"error\",\n        showIcon: true,\n        closable: true,\n        onClose: () => setError(null),\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 4,\n        children: \"Security Events\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: loading,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          dataSource: events,\n          columns: columns,\n          rowKey: \"id\",\n          pagination: {\n            pageSize: 10\n          },\n          expandable: {\n            expandedRowRender: record => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"User Agent:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 22\n                }, this), \" \", record.userAgent || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), record.details && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Details:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 41\n                }, this), \" \", record.details]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 38\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n_s(SecurityDashboard, \"5YrxRhw/WvaHkvMN2RETbMWOTaY=\", false, function () {\n  return [useAuth];\n});\n_c = SecurityDashboard;\nexport default SecurityDashboard;\nvar _c;\n$RefreshReg$(_c, \"SecurityDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "Tag", "Typography", "Statistic", "Row", "Col", "DatePicker", "<PERSON><PERSON>", "Space", "<PERSON><PERSON>", "Spin", "SecurityScanOutlined", "WarningOutlined", "CheckCircleOutlined", "CloseCircleOutlined", "ExclamationCircleOutlined", "ReloadOutlined", "useAuth", "format", "jsxDEV", "_jsxDEV", "Title", "Text", "RangePicker", "SecurityEventType", "SecurityDashboard", "_s", "admin", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "events", "setEvents", "stats", "setStats", "totalEvents", "successfulLogins", "failed<PERSON>ogins", "suspiciousActivities", "accountLockouts", "twoFactorSuccesses", "twoFactorFailures", "date<PERSON><PERSON><PERSON>", "setDateRange", "error", "setError", "fetchSecurityEvents", "params", "startDate", "toISOString", "endDate", "response", "secureApiService", "get", "success", "data", "message", "err", "_err$response", "_err$response$data", "handleDateRangeChange", "dates", "toDate", "handleRefresh", "getEventTypeTag", "eventType", "LOGIN_SUCCESS", "color", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "LOGIN_FAILURE", "LOGOUT", "PASSWORD_RESET", "ACCOUNT_LOCKED", "SUSPICIOUS_ACTIVITY", "TWO_FACTOR_SUCCESS", "TWO_FACTOR_FAILURE", "getSeverityTag", "severity", "columns", "title", "dataIndex", "key", "render", "text", "Date", "sorter", "a", "b", "timestamp", "getTime", "filters", "Object", "values", "map", "type", "value", "onFilter", "record", "toString", "className", "level", "description", "showIcon", "gutter", "xs", "sm", "md", "lg", "prefix", "valueStyle", "direction", "size", "onChange", "onClick", "closable", "onClose", "spinning", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "expandable", "expandedRowRender", "userAgent", "details", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Table, Tag, Typography, Statistic, Row, Col, DatePicker, Button, Space, Alert, Spin } from 'antd';\nimport {\n  SecurityScanOutlined,\n  WarningOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  ExclamationCircleOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport authService from '../../services/authService';\nimport { DateFormat } from '../../utils/dateUtils';\nimport { format } from 'date-fns';\nimport { ApiResponse } from '../../types/api';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\n\n// Define security event types\nenum SecurityEventType {\n  LOGIN_SUCCESS = 'LOGIN_SUCCESS',\n  LOGIN_FAILURE = 'LOGIN_FAILURE',\n  LOGOUT = 'LOGOUT',\n  PASSWORD_RESET = 'PASSWORD_RESET',\n  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',\n  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',\n  TWO_FACTOR_SUCCESS = 'TWO_FACTOR_SUCCESS',\n  TWO_FACTOR_FAILURE = 'TWO_FACTOR_FAILURE',\n}\n\n// Define security event interface\ninterface SecurityEvent {\n  id: number;\n  eventType: SecurityEventType;\n  message: string;\n  userId?: number;\n  email?: string;\n  ip: string;\n  userAgent?: string;\n  timestamp: string;\n  details?: string;\n  severity: 'info' | 'warning' | 'error' | 'critical';\n}\n\n// Define security stats interface\ninterface SecurityStats {\n  totalEvents: number;\n  successfulLogins: number;\n  failedLogins: number;\n  suspiciousActivities: number;\n  accountLockouts: number;\n  twoFactorSuccesses: number;\n  twoFactorFailures: number;\n}\n\nconst SecurityDashboard: React.FC = () => {\n  const { admin } = useAuth();\n  const isMainAdmin = admin?.isMainAdmin || false;\n  const [loading, setLoading] = useState<boolean>(true);\n  const [events, setEvents] = useState<SecurityEvent[]>([]);\n  const [stats, setStats] = useState<SecurityStats>({\n    totalEvents: 0,\n    successfulLogins: 0,\n    failedLogins: 0,\n    suspiciousActivities: 0,\n    accountLockouts: 0,\n    twoFactorSuccesses: 0,\n    twoFactorFailures: 0,\n  });\n  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch security events\n  const fetchSecurityEvents = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Prepare date range parameters\n      const params: Record<string, string> = {};\n      if (dateRange) {\n        params.startDate = dateRange[0].toISOString();\n        params.endDate = dateRange[1].toISOString();\n      }\n\n      // Fetch events from API\n      const response = await secureApiService.get<ApiResponse<{events: SecurityEvent[], stats: SecurityStats}>>('/admin/security/events', { params });\n\n      if (response.success && response.data) {\n        setEvents(response.data.events);\n        setStats(response.data.stats);\n      } else {\n        setError(response.message || 'Failed to fetch security events');\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchSecurityEvents();\n  }, []);\n\n  // Handle date range change\n  const handleDateRangeChange = (dates: any) => {\n    if (dates) {\n      setDateRange([dates[0].toDate(), dates[1].toDate()]);\n    } else {\n      setDateRange(null);\n    }\n  };\n\n  // Handle refresh button click\n  const handleRefresh = () => {\n    fetchSecurityEvents();\n  };\n\n  // Get tag color based on event type\n  const getEventTypeTag = (eventType: SecurityEventType) => {\n    switch (eventType) {\n      case SecurityEventType.LOGIN_SUCCESS:\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>Login Success</Tag>;\n      case SecurityEventType.LOGIN_FAILURE:\n        return <Tag color=\"error\" icon={<CloseCircleOutlined />}>Login Failure</Tag>;\n      case SecurityEventType.LOGOUT:\n        return <Tag color=\"default\">Logout</Tag>;\n      case SecurityEventType.PASSWORD_RESET:\n        return <Tag color=\"processing\">Password Reset</Tag>;\n      case SecurityEventType.ACCOUNT_LOCKED:\n        return <Tag color=\"error\" icon={<ExclamationCircleOutlined />}>Account Locked</Tag>;\n      case SecurityEventType.SUSPICIOUS_ACTIVITY:\n        return <Tag color=\"warning\" icon={<WarningOutlined />}>Suspicious Activity</Tag>;\n      case SecurityEventType.TWO_FACTOR_SUCCESS:\n        return <Tag color=\"success\">2FA Success</Tag>;\n      case SecurityEventType.TWO_FACTOR_FAILURE:\n        return <Tag color=\"error\">2FA Failure</Tag>;\n      default:\n        return <Tag>Unknown</Tag>;\n    }\n  };\n\n  // Get severity tag\n  const getSeverityTag = (severity: string) => {\n    switch (severity) {\n      case 'info':\n        return <Tag color=\"blue\">Info</Tag>;\n      case 'warning':\n        return <Tag color=\"orange\">Warning</Tag>;\n      case 'error':\n        return <Tag color=\"red\">Error</Tag>;\n      case 'critical':\n        return <Tag color=\"volcano\" icon={<WarningOutlined />}>Critical</Tag>;\n      default:\n        return <Tag>Unknown</Tag>;\n    }\n  };\n\n  // Table columns\n  const columns = [\n    {\n      title: 'Time',\n      dataIndex: 'timestamp',\n      key: 'timestamp',\n      render: (text: string) => format(new Date(text), 'yyyy-MM-dd HH:mm:ss'),\n      sorter: (a: SecurityEvent, b: SecurityEvent) =>\n        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),\n    },\n    {\n      title: 'Event',\n      dataIndex: 'eventType',\n      key: 'eventType',\n      render: (text: SecurityEventType) => getEventTypeTag(text),\n      filters: Object.values(SecurityEventType).map(type => ({ text: type, value: type })),\n      onFilter: (value: any, record: SecurityEvent) => record.eventType === value.toString(),\n    },\n    {\n      title: 'User',\n      dataIndex: 'email',\n      key: 'email',\n      render: (text: string, record: SecurityEvent) => text || 'N/A',\n    },\n    {\n      title: 'IP Address',\n      dataIndex: 'ip',\n      key: 'ip',\n    },\n    {\n      title: 'Severity',\n      dataIndex: 'severity',\n      key: 'severity',\n      render: (text: string) => getSeverityTag(text),\n      filters: [\n        { text: 'Info', value: 'info' },\n        { text: 'Warning', value: 'warning' },\n        { text: 'Error', value: 'error' },\n        { text: 'Critical', value: 'critical' },\n      ],\n      onFilter: (value: any, record: SecurityEvent) => record.severity === value.toString(),\n    },\n    {\n      title: 'Message',\n      dataIndex: 'message',\n      key: 'message',\n    },\n  ];\n\n  return (\n    <div className=\"p-6\">\n      <Title level={2}>\n        <SecurityScanOutlined /> Security Dashboard\n      </Title>\n\n      <Text className=\"mb-6 block text-gray-600\">\n        Monitor security events and detect potential threats to your system.\n      </Text>\n\n      {!isMainAdmin && (\n        <Alert\n          message=\"Limited Access\"\n          description=\"You have limited access to security information. Contact the main administrator for full access.\"\n          type=\"warning\"\n          showIcon\n          className=\"mb-6\"\n        />\n      )}\n\n      {/* Security Stats */}\n      <Card className=\"mb-6 shadow-md\">\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Statistic\n              title=\"Total Events\"\n              value={stats.totalEvents}\n              prefix={<SecurityScanOutlined />}\n            />\n          </Col>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Statistic\n              title=\"Successful Logins\"\n              value={stats.successfulLogins}\n              valueStyle={{ color: '#3f8600' }}\n              prefix={<CheckCircleOutlined />}\n            />\n          </Col>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Statistic\n              title=\"Failed Logins\"\n              value={stats.failedLogins}\n              valueStyle={{ color: '#cf1322' }}\n              prefix={<CloseCircleOutlined />}\n            />\n          </Col>\n          <Col xs={24} sm={12} md={8} lg={6}>\n            <Statistic\n              title=\"Suspicious Activities\"\n              value={stats.suspiciousActivities}\n              valueStyle={{ color: '#faad14' }}\n              prefix={<WarningOutlined />}\n            />\n          </Col>\n        </Row>\n      </Card>\n\n      {/* Filters and Controls */}\n      <Card className=\"mb-6 shadow-md\">\n        <Space direction=\"horizontal\" size=\"middle\" className=\"mb-4\">\n          <RangePicker onChange={handleDateRangeChange} />\n          <Button\n            type=\"primary\"\n            icon={<ReloadOutlined />}\n            onClick={handleRefresh}\n            loading={loading}\n          >\n            Refresh\n          </Button>\n        </Space>\n\n        {error && (\n          <Alert\n            message=\"Error\"\n            description={error}\n            type=\"error\"\n            showIcon\n            closable\n            onClose={() => setError(null)}\n            className=\"mb-4\"\n          />\n        )}\n      </Card>\n\n      {/* Security Events Table */}\n      <Card className=\"shadow-md\">\n        <Title level={4}>Security Events</Title>\n        <Spin spinning={loading}>\n          <Table\n            dataSource={events}\n            columns={columns}\n            rowKey=\"id\"\n            pagination={{ pageSize: 10 }}\n            expandable={{\n              expandedRowRender: (record) => (\n                <div className=\"p-4 bg-gray-50\">\n                  <p><strong>User Agent:</strong> {record.userAgent || 'N/A'}</p>\n                  {record.details && <p><strong>Details:</strong> {record.details}</p>}\n                </div>\n              ),\n            }}\n          />\n        </Spin>\n      </Card>\n    </div>\n  );\n};\n\nexport default SecurityDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,QAAQ,MAAM;AAChH,SACEC,oBAAoB,EACpBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,yBAAyB,EACzBC,cAAc,QACT,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,4BAA4B;AAGpD,SAASC,MAAM,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAY,CAAC,GAAGjB,UAAU;;AAElC;AAAA,IACKkB,iBAAiB,0BAAjBA,iBAAiB;EAAjBA,iBAAiB;EAAjBA,iBAAiB;EAAjBA,iBAAiB;EAAjBA,iBAAiB;EAAjBA,iBAAiB;EAAjBA,iBAAiB;EAAjBA,iBAAiB;EAAjBA,iBAAiB;EAAA,OAAjBA,iBAAiB;AAAA,EAAjBA,iBAAiB,SAWtB;AAcA;AAWA,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM;IAAEC;EAAM,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC3B,MAAMW,WAAW,GAAG,CAAAD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,WAAW,KAAI,KAAK;EAC/C,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAU,IAAI,CAAC;EACrD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAkB,EAAE,CAAC;EACzD,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAgB;IAChDsC,WAAW,EAAE,CAAC;IACdC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE,CAAC;IACfC,oBAAoB,EAAE,CAAC;IACvBC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAsB,IAAI,CAAC;EACrE,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAMiD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBe,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAME,MAA8B,GAAG,CAAC,CAAC;MACzC,IAAIL,SAAS,EAAE;QACbK,MAAM,CAACC,SAAS,GAAGN,SAAS,CAAC,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;QAC7CF,MAAM,CAACG,OAAO,GAAGR,SAAS,CAAC,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;MAC7C;;MAEA;MACA,MAAME,QAAQ,GAAG,MAAMC,gBAAgB,CAACC,GAAG,CAA+D,wBAAwB,EAAE;QAAEN;MAAO,CAAC,CAAC;MAE/I,IAAII,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;QACrCvB,SAAS,CAACmB,QAAQ,CAACI,IAAI,CAACxB,MAAM,CAAC;QAC/BG,QAAQ,CAACiB,QAAQ,CAACI,IAAI,CAACtB,KAAK,CAAC;MAC/B,CAAC,MAAM;QACLY,QAAQ,CAACM,QAAQ,CAACK,OAAO,IAAI,iCAAiC,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBd,QAAQ,CAAC,EAAAa,aAAA,GAAAD,GAAG,CAACN,QAAQ,cAAAO,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,IAAI,mBAAmB,CAAC;IAC7E,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAhC,SAAS,CAAC,MAAM;IACdgD,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMc,qBAAqB,GAAIC,KAAU,IAAK;IAC5C,IAAIA,KAAK,EAAE;MACTlB,YAAY,CAAC,CAACkB,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,EAAED,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC,MAAM;MACLnB,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMoB,aAAa,GAAGA,CAAA,KAAM;IAC1BjB,mBAAmB,CAAC,CAAC;EACvB,CAAC;;EAED;EACA,MAAMkB,eAAe,GAAIC,SAA4B,IAAK;IACxD,QAAQA,SAAS;MACf,KAAKzC,iBAAiB,CAAC0C,aAAa;QAClC,oBAAO9C,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,SAAS;UAACC,IAAI,eAAEhD,OAAA,CAACP,mBAAmB;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAa;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAChF,KAAKhD,iBAAiB,CAACkD,aAAa;QAClC,oBAAOtD,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,OAAO;UAACC,IAAI,eAAEhD,OAAA,CAACN,mBAAmB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAa;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC9E,KAAKhD,iBAAiB,CAACmD,MAAM;QAC3B,oBAAOvD,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,SAAS;UAAAM,QAAA,EAAC;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC1C,KAAKhD,iBAAiB,CAACoD,cAAc;QACnC,oBAAOxD,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,YAAY;UAAAM,QAAA,EAAC;QAAc;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACrD,KAAKhD,iBAAiB,CAACqD,cAAc;QACnC,oBAAOzD,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,OAAO;UAACC,IAAI,eAAEhD,OAAA,CAACL,yBAAyB;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAc;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACrF,KAAKhD,iBAAiB,CAACsD,mBAAmB;QACxC,oBAAO1D,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,SAAS;UAACC,IAAI,eAAEhD,OAAA,CAACR,eAAe;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAmB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAClF,KAAKhD,iBAAiB,CAACuD,kBAAkB;QACvC,oBAAO3D,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,SAAS;UAAAM,QAAA,EAAC;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC/C,KAAKhD,iBAAiB,CAACwD,kBAAkB;QACvC,oBAAO5D,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,OAAO;UAAAM,QAAA,EAAC;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC7C;QACE,oBAAOpD,OAAA,CAACnB,GAAG;UAAAwE,QAAA,EAAC;QAAO;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMS,cAAc,GAAIC,QAAgB,IAAK;IAC3C,QAAQA,QAAQ;MACd,KAAK,MAAM;QACT,oBAAO9D,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,MAAM;UAAAM,QAAA,EAAC;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACrC,KAAK,SAAS;QACZ,oBAAOpD,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,QAAQ;UAAAM,QAAA,EAAC;QAAO;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC1C,KAAK,OAAO;QACV,oBAAOpD,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,KAAK;UAAAM,QAAA,EAAC;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACrC,KAAK,UAAU;QACb,oBAAOpD,OAAA,CAACnB,GAAG;UAACkE,KAAK,EAAC,SAAS;UAACC,IAAI,eAAEhD,OAAA,CAACR,eAAe;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAQ;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvE;QACE,oBAAOpD,OAAA,CAACnB,GAAG;UAAAwE,QAAA,EAAC;QAAO;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMW,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGC,IAAY,IAAKtE,MAAM,CAAC,IAAIuE,IAAI,CAACD,IAAI,CAAC,EAAE,qBAAqB,CAAC;IACvEE,MAAM,EAAEA,CAACC,CAAgB,EAAEC,CAAgB,KACzC,IAAIH,IAAI,CAACE,CAAC,CAACE,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAIL,IAAI,CAACG,CAAC,CAACC,SAAS,CAAC,CAACC,OAAO,CAAC;EACpE,CAAC,EACD;IACEV,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGC,IAAuB,IAAKxB,eAAe,CAACwB,IAAI,CAAC;IAC1DO,OAAO,EAAEC,MAAM,CAACC,MAAM,CAACzE,iBAAiB,CAAC,CAAC0E,GAAG,CAACC,IAAI,KAAK;MAAEX,IAAI,EAAEW,IAAI;MAAEC,KAAK,EAAED;IAAK,CAAC,CAAC,CAAC;IACpFE,QAAQ,EAAEA,CAACD,KAAU,EAAEE,MAAqB,KAAKA,MAAM,CAACrC,SAAS,KAAKmC,KAAK,CAACG,QAAQ,CAAC;EACvF,CAAC,EACD;IACEnB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACC,IAAY,EAAEc,MAAqB,KAAKd,IAAI,IAAI;EAC3D,CAAC,EACD;IACEJ,KAAK,EAAE,YAAY;IACnBC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGC,IAAY,IAAKP,cAAc,CAACO,IAAI,CAAC;IAC9CO,OAAO,EAAE,CACP;MAAEP,IAAI,EAAE,MAAM;MAAEY,KAAK,EAAE;IAAO,CAAC,EAC/B;MAAEZ,IAAI,EAAE,SAAS;MAAEY,KAAK,EAAE;IAAU,CAAC,EACrC;MAAEZ,IAAI,EAAE,OAAO;MAAEY,KAAK,EAAE;IAAQ,CAAC,EACjC;MAAEZ,IAAI,EAAE,UAAU;MAAEY,KAAK,EAAE;IAAW,CAAC,CACxC;IACDC,QAAQ,EAAEA,CAACD,KAAU,EAAEE,MAAqB,KAAKA,MAAM,CAACpB,QAAQ,KAAKkB,KAAK,CAACG,QAAQ,CAAC;EACtF,CAAC,EACD;IACEnB,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE;EACP,CAAC,CACF;EAED,oBACElE,OAAA;IAAKoF,SAAS,EAAC,KAAK;IAAA/B,QAAA,gBAClBrD,OAAA,CAACC,KAAK;MAACoF,KAAK,EAAE,CAAE;MAAAhC,QAAA,gBACdrD,OAAA,CAACT,oBAAoB;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,uBAC1B;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERpD,OAAA,CAACE,IAAI;MAACkF,SAAS,EAAC,0BAA0B;MAAA/B,QAAA,EAAC;IAE3C;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAEN,CAAC5C,WAAW,iBACXR,OAAA,CAACX,KAAK;MACJ+C,OAAO,EAAC,gBAAgB;MACxBkD,WAAW,EAAC,kGAAkG;MAC9GP,IAAI,EAAC,SAAS;MACdQ,QAAQ;MACRH,SAAS,EAAC;IAAM;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACF,eAGDpD,OAAA,CAACrB,IAAI;MAACyG,SAAS,EAAC,gBAAgB;MAAA/B,QAAA,eAC9BrD,OAAA,CAAChB,GAAG;QAACwG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAnC,QAAA,gBACpBrD,OAAA,CAACf,GAAG;UAACwG,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAChCrD,OAAA,CAACjB,SAAS;YACRiF,KAAK,EAAC,cAAc;YACpBgB,KAAK,EAAEnE,KAAK,CAACE,WAAY;YACzB8E,MAAM,eAAE7F,OAAA,CAACT,oBAAoB;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpD,OAAA,CAACf,GAAG;UAACwG,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAChCrD,OAAA,CAACjB,SAAS;YACRiF,KAAK,EAAC,mBAAmB;YACzBgB,KAAK,EAAEnE,KAAK,CAACG,gBAAiB;YAC9B8E,UAAU,EAAE;cAAE/C,KAAK,EAAE;YAAU,CAAE;YACjC8C,MAAM,eAAE7F,OAAA,CAACP,mBAAmB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpD,OAAA,CAACf,GAAG;UAACwG,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAChCrD,OAAA,CAACjB,SAAS;YACRiF,KAAK,EAAC,eAAe;YACrBgB,KAAK,EAAEnE,KAAK,CAACI,YAAa;YAC1B6E,UAAU,EAAE;cAAE/C,KAAK,EAAE;YAAU,CAAE;YACjC8C,MAAM,eAAE7F,OAAA,CAACN,mBAAmB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpD,OAAA,CAACf,GAAG;UAACwG,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAvC,QAAA,eAChCrD,OAAA,CAACjB,SAAS;YACRiF,KAAK,EAAC,uBAAuB;YAC7BgB,KAAK,EAAEnE,KAAK,CAACK,oBAAqB;YAClC4E,UAAU,EAAE;cAAE/C,KAAK,EAAE;YAAU,CAAE;YACjC8C,MAAM,eAAE7F,OAAA,CAACR,eAAe;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPpD,OAAA,CAACrB,IAAI;MAACyG,SAAS,EAAC,gBAAgB;MAAA/B,QAAA,gBAC9BrD,OAAA,CAACZ,KAAK;QAAC2G,SAAS,EAAC,YAAY;QAACC,IAAI,EAAC,QAAQ;QAACZ,SAAS,EAAC,MAAM;QAAA/B,QAAA,gBAC1DrD,OAAA,CAACG,WAAW;UAAC8F,QAAQ,EAAEzD;QAAsB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDpD,OAAA,CAACb,MAAM;UACL4F,IAAI,EAAC,SAAS;UACd/B,IAAI,eAAEhD,OAAA,CAACJ,cAAc;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzB8C,OAAO,EAAEvD,aAAc;UACvBlC,OAAO,EAAEA,OAAQ;UAAA4C,QAAA,EAClB;QAED;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAEP5B,KAAK,iBACJxB,OAAA,CAACX,KAAK;QACJ+C,OAAO,EAAC,OAAO;QACfkD,WAAW,EAAE9D,KAAM;QACnBuD,IAAI,EAAC,OAAO;QACZQ,QAAQ;QACRY,QAAQ;QACRC,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAAC,IAAI,CAAE;QAC9B2D,SAAS,EAAC;MAAM;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPpD,OAAA,CAACrB,IAAI;MAACyG,SAAS,EAAC,WAAW;MAAA/B,QAAA,gBACzBrD,OAAA,CAACC,KAAK;QAACoF,KAAK,EAAE,CAAE;QAAAhC,QAAA,EAAC;MAAe;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACxCpD,OAAA,CAACV,IAAI;QAAC+G,QAAQ,EAAE5F,OAAQ;QAAA4C,QAAA,eACtBrD,OAAA,CAACpB,KAAK;UACJ0H,UAAU,EAAE3F,MAAO;UACnBoD,OAAO,EAAEA,OAAQ;UACjBwC,MAAM,EAAC,IAAI;UACXC,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAC7BC,UAAU,EAAE;YACVC,iBAAiB,EAAGzB,MAAM,iBACxBlF,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAA/B,QAAA,gBAC7BrD,OAAA;gBAAAqD,QAAA,gBAAGrD,OAAA;kBAAAqD,QAAA,EAAQ;gBAAW;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC8B,MAAM,CAAC0B,SAAS,IAAI,KAAK;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC9D8B,MAAM,CAAC2B,OAAO,iBAAI7G,OAAA;gBAAAqD,QAAA,gBAAGrD,OAAA;kBAAAqD,QAAA,EAAQ;gBAAQ;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC8B,MAAM,CAAC2B,OAAO;cAAA;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE;UAET;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC9C,EAAA,CApQID,iBAA2B;EAAA,QACbR,OAAO;AAAA;AAAAiH,EAAA,GADrBzG,iBAA2B;AAsQjC,eAAeA,iBAAiB;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}