{"ast": null, "code": "import axios from 'axios';\n\n/**\n * New secure authentication service using HTTP-only cookies\n */\nclass AuthService {\n  constructor() {\n    this.api = void 0;\n    this.api = axios.create({\n      baseURL: (process.env.REACT_APP_API_URL || 'http://localhost:5000') + '/api',\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      withCredentials: true // Essential for HTTP-only cookies\n    });\n\n    // Setup response interceptor for error handling\n    this.api.interceptors.response.use(response => response, error => {\n      var _error$response;\n      // Handle 401 Unauthorized - redirect to login\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        // Only redirect if not already on login page\n        if (!window.location.pathname.includes('/login')) {\n          window.location.href = '/admin/login';\n        }\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  /**\n   * Admin login\n   */\n  async adminLogin(email, password) {\n    try {\n      const response = await this.api.post('/auth/admin/login', {\n        email,\n        password\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || 'Login failed');\n    }\n  }\n\n  /**\n   * Admin logout\n   */\n  async adminLogout() {\n    try {\n      const response = await this.api.post('/auth/admin/logout');\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || error.message || 'Logout failed');\n    }\n  }\n\n  /**\n   * Get current admin profile\n   */\n  async getAdminProfile() {\n    try {\n      const response = await this.api.get('/auth/admin/profile');\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || error.message || 'Failed to get admin profile');\n    }\n  }\n\n  /**\n   * Verify authentication status\n   */\n  async verifyAuth() {\n    try {\n      const response = await this.api.get('/auth/verify');\n      return response.data;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      throw new Error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || error.message || 'Authentication verification failed');\n    }\n  }\n\n  /**\n   * Verify admin authentication status\n   */\n  async verifyAdminAuth() {\n    try {\n      const response = await this.api.get('/auth/admin/verify');\n      return response.data;\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      throw new Error(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || error.message || 'Admin authentication verification failed');\n    }\n  }\n\n  /**\n   * Generic API request method for authenticated requests\n   */\n  async request(method, url, data) {\n    try {\n      const response = await this.api.request({\n        method,\n        url,\n        data\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      throw new Error(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || error.message || 'Request failed');\n    }\n  }\n}\n\n// Export singleton instance\nexport const authService = new AuthService();\nexport default authService;", "map": {"version": 3, "names": ["axios", "AuthService", "constructor", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "withCredentials", "interceptors", "response", "use", "error", "_error$response", "status", "window", "location", "pathname", "includes", "href", "Promise", "reject", "adminLogin", "email", "password", "post", "data", "_error$response2", "_error$response2$data", "Error", "message", "adminLogout", "_error$response3", "_error$response3$data", "getAdminProfile", "get", "_error$response4", "_error$response4$data", "verifyAuth", "_error$response5", "_error$response5$data", "verifyAdminAuth", "_error$response6", "_error$response6$data", "request", "method", "url", "_error$response7", "_error$response7$data", "authService"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse } from 'axios';\n\n/**\n * New secure authentication service using HTTP-only cookies\n */\nclass AuthService {\n  private api: AxiosInstance;\n\n  constructor() {\n    this.api = axios.create({\n      baseURL: (process.env.REACT_APP_API_URL || 'http://localhost:5000') + '/api',\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      withCredentials: true, // Essential for HTTP-only cookies\n    });\n\n    // Setup response interceptor for error handling\n    this.api.interceptors.response.use(\n      (response: AxiosResponse) => response,\n      (error) => {\n        // Handle 401 Unauthorized - redirect to login\n        if (error.response?.status === 401) {\n          // Only redirect if not already on login page\n          if (!window.location.pathname.includes('/login')) {\n            window.location.href = '/admin/login';\n          }\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  /**\n   * Admin login\n   */\n  async adminLogin(email: string, password: string): Promise<any> {\n    try {\n      const response = await this.api.post('/auth/admin/login', {\n        email,\n        password\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.message || \n        error.message || \n        'Login failed'\n      );\n    }\n  }\n\n  /**\n   * Admin logout\n   */\n  async adminLogout(): Promise<any> {\n    try {\n      const response = await this.api.post('/auth/admin/logout');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.message || \n        error.message || \n        'Logout failed'\n      );\n    }\n  }\n\n  /**\n   * Get current admin profile\n   */\n  async getAdminProfile(): Promise<any> {\n    try {\n      const response = await this.api.get('/auth/admin/profile');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.message || \n        error.message || \n        'Failed to get admin profile'\n      );\n    }\n  }\n\n  /**\n   * Verify authentication status\n   */\n  async verifyAuth(): Promise<any> {\n    try {\n      const response = await this.api.get('/auth/verify');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.message || \n        error.message || \n        'Authentication verification failed'\n      );\n    }\n  }\n\n  /**\n   * Verify admin authentication status\n   */\n  async verifyAdminAuth(): Promise<any> {\n    try {\n      const response = await this.api.get('/auth/admin/verify');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.message || \n        error.message || \n        'Admin authentication verification failed'\n      );\n    }\n  }\n\n  /**\n   * Generic API request method for authenticated requests\n   */\n  async request(method: 'GET' | 'POST' | 'PUT' | 'DELETE', url: string, data?: any): Promise<any> {\n    try {\n      const response = await this.api.request({\n        method,\n        url,\n        data\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.message || \n        error.message || \n        'Request failed'\n      );\n    }\n  }\n}\n\n// Export singleton instance\nexport const authService = new AuthService();\nexport default authService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAwC,OAAO;;AAE3D;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EAGhBC,WAAWA,CAAA,EAAG;IAAA,KAFNC,GAAG;IAGT,IAAI,CAACA,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;MACtBC,OAAO,EAAE,CAACC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,IAAI,MAAM;MAC5EC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,eAAe,EAAE,IAAI,CAAE;IACzB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACR,GAAG,CAACS,YAAY,CAACC,QAAQ,CAACC,GAAG,CAC/BD,QAAuB,IAAKA,QAAQ,EACpCE,KAAK,IAAK;MAAA,IAAAC,eAAA;MACT;MACA,IAAI,EAAAA,eAAA,GAAAD,KAAK,CAACF,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClC;QACA,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;UAChDH,MAAM,CAACC,QAAQ,CAACG,IAAI,GAAG,cAAc;QACvC;MACF;MACA,OAAOC,OAAO,CAACC,MAAM,CAACT,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAMU,UAAUA,CAACC,KAAa,EAAEC,QAAgB,EAAgB;IAC9D,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAM,IAAI,CAACV,GAAG,CAACyB,IAAI,CAAC,mBAAmB,EAAE;QACxDF,KAAK;QACLC;MACF,CAAC,CAAC;MACF,OAAOd,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAU,EAAE;MAAA,IAAAe,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIC,KAAK,CACb,EAAAF,gBAAA,GAAAf,KAAK,CAACF,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBD,IAAI,cAAAE,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAC7BlB,KAAK,CAACkB,OAAO,IACb,cACF,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMC,WAAWA,CAAA,EAAiB;IAChC,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAM,IAAI,CAACV,GAAG,CAACyB,IAAI,CAAC,oBAAoB,CAAC;MAC1D,OAAOf,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIJ,KAAK,CACb,EAAAG,gBAAA,GAAApB,KAAK,CAACF,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBN,IAAI,cAAAO,qBAAA,uBAApBA,qBAAA,CAAsBH,OAAO,KAC7BlB,KAAK,CAACkB,OAAO,IACb,eACF,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMI,eAAeA,CAAA,EAAiB;IACpC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAM,IAAI,CAACV,GAAG,CAACmC,GAAG,CAAC,qBAAqB,CAAC;MAC1D,OAAOzB,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAU,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIR,KAAK,CACb,EAAAO,gBAAA,GAAAxB,KAAK,CAACF,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBP,OAAO,KAC7BlB,KAAK,CAACkB,OAAO,IACb,6BACF,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMQ,UAAUA,CAAA,EAAiB;IAC/B,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAM,IAAI,CAACV,GAAG,CAACmC,GAAG,CAAC,cAAc,CAAC;MACnD,OAAOzB,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAU,EAAE;MAAA,IAAA2B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIX,KAAK,CACb,EAAAU,gBAAA,GAAA3B,KAAK,CAACF,QAAQ,cAAA6B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBb,IAAI,cAAAc,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAC7BlB,KAAK,CAACkB,OAAO,IACb,oCACF,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMW,eAAeA,CAAA,EAAiB;IACpC,IAAI;MACF,MAAM/B,QAAQ,GAAG,MAAM,IAAI,CAACV,GAAG,CAACmC,GAAG,CAAC,oBAAoB,CAAC;MACzD,OAAOzB,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAU,EAAE;MAAA,IAAA8B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAId,KAAK,CACb,EAAAa,gBAAA,GAAA9B,KAAK,CAACF,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBb,OAAO,KAC7BlB,KAAK,CAACkB,OAAO,IACb,0CACF,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMc,OAAOA,CAACC,MAAyC,EAAEC,GAAW,EAAEpB,IAAU,EAAgB;IAC9F,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAM,IAAI,CAACV,GAAG,CAAC4C,OAAO,CAAC;QACtCC,MAAM;QACNC,GAAG;QACHpB;MACF,CAAC,CAAC;MACF,OAAOhB,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOd,KAAU,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAInB,KAAK,CACb,EAAAkB,gBAAA,GAAAnC,KAAK,CAACF,QAAQ,cAAAqC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBlB,OAAO,KAC7BlB,KAAK,CAACkB,OAAO,IACb,gBACF,CAAC;IACH;EACF;AACF;;AAEA;AACA,OAAO,MAAMmB,WAAW,GAAG,IAAInD,WAAW,CAAC,CAAC;AAC5C,eAAemD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}