{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Table, Button, Modal, Form, Input, Select, message, Space, Card, Typography, Popconfirm, Tag } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst AdminManagement = () => {\n  _s();\n  const {\n    admin: currentAdmin\n  } = useAuth();\n  const isMainAdmin = (currentAdmin === null || currentAdmin === void 0 ? void 0 : currentAdmin.isMainAdmin) || false;\n  const navigate = useNavigate();\n  const [admins, setAdmins] = useState([]);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState(null);\n  const [form] = Form.useForm();\n\n  // Redirect non-main admins away from this page\n  useEffect(() => {\n    if (!isMainAdmin) {\n      message.error('Access denied. Only the main admin can access this page.');\n      navigate('/admin/dashboard', {\n        replace: true\n      });\n    }\n  }, [isMainAdmin, navigate]);\n  useEffect(() => {\n    fetchAdmins();\n\n    // Debug: Log when component mounts\n    console.log('AdminManagement component mounted');\n\n    // Add event listener to log localStorage changes\n    const originalSetItem = localStorage.setItem;\n    localStorage.setItem = function (key, value) {\n      console.log(`localStorage.setItem called with key \"${key}\":`, value);\n      originalSetItem.call(this, key, value);\n    };\n    return () => {\n      // Restore original function when component unmounts\n      localStorage.setItem = originalSetItem;\n      console.log('AdminManagement component unmounted');\n    };\n  }, []);\n  const fetchAdmins = async () => {\n    try {\n      console.log('Fetching admins from API...');\n\n      // Get the admin token from localStorage\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        console.error('No admin token found');\n        message.error('Authentication error. Please log in again.');\n        return;\n      }\n\n      // Fetch admins from the API\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/all`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`Failed to fetch admins: ${response.status} ${response.statusText}`);\n      }\n      const data = await response.json();\n      console.log('Admins loaded from API:', data);\n      setAdmins(data);\n    } catch (error) {\n      console.error('Error in fetchAdmins:', error);\n      message.error('Failed to fetch admins');\n    }\n  };\n  const handleCreateAdmin = async values => {\n    try {\n      console.log('Creating admin with values:', values);\n\n      // Check if we're trying to create a main admin\n      if (values.email.toLowerCase() === '<EMAIL>') {\n        message.error('Cannot create another Main Admin account');\n        return;\n      }\n\n      // Get the admin token from localStorage\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        console.error('No admin token found');\n        message.error('Authentication error. Please log in again.');\n        return;\n      }\n\n      // Create admin through the API\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/create`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          name: values.name,\n          email: values.email,\n          password: values.password,\n          role: values.role\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || `Failed to create admin: ${response.status} ${response.statusText}`);\n      }\n      const newAdmin = await response.json();\n      console.log('New admin created via API:', newAdmin);\n\n      // Refresh the admin list\n      fetchAdmins();\n      message.success('Admin created successfully');\n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      if (error instanceof Error && error.message.includes('Admin already exists')) {\n        message.error('An admin with this email already exists');\n      } else {\n        message.error('Failed to create admin');\n        console.error('Error creating admin:', error);\n      }\n    }\n  };\n  const handleEdit = record => {\n    setEditingAdmin(record);\n    form.setFieldsValue({\n      name: record.name,\n      email: record.email,\n      role: record.role\n    });\n    setIsModalVisible(true);\n  };\n  const handleUpdateAdmin = async values => {\n    if (!editingAdmin) return;\n    try {\n      console.log('Updating admin with ID:', editingAdmin.id, 'Values:', values);\n\n      // Mock updating an admin\n      const updatedAdmins = admins.map(admin => {\n        if (admin.id === editingAdmin.id) {\n          return {\n            ...admin,\n            name: values.name,\n            email: values.email,\n            role: values.role,\n            privileges: values.role === 'super_admin' ? ['view', 'edit', 'delete'] : ['view', 'edit']\n          };\n        }\n        return admin;\n      });\n\n      // Save to localStorage\n      localStorage.setItem('admins', JSON.stringify(updatedAdmins));\n      setAdmins(updatedAdmins);\n      message.success('Admin updated successfully');\n      setIsModalVisible(false);\n      form.resetFields();\n      setEditingAdmin(null);\n    } catch (error) {\n      message.error('Failed to update admin');\n      console.error('Error updating admin:', error);\n    }\n  };\n  const handleDelete = async id => {\n    try {\n      console.log('Deleting admin with ID:', id);\n\n      // Check if this is the main admin\n      const adminToDelete = admins.find(admin => admin.id === id);\n      if (adminToDelete !== null && adminToDelete !== void 0 && adminToDelete.isMainAdmin) {\n        message.error('The main admin account cannot be deleted');\n        return;\n      }\n\n      // Get the admin token from localStorage\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        console.error('No admin token found');\n        message.error('Authentication error. Please log in again.');\n        return;\n      }\n\n      // Delete admin through the API\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || `Failed to delete admin: ${response.status} ${response.statusText}`);\n      }\n\n      // Refresh the admin list\n      fetchAdmins();\n      message.success('Admin deleted successfully');\n    } catch (error) {\n      message.error('Failed to delete admin');\n      console.error('Error deleting admin:', error);\n    }\n  };\n  const columns = [{\n    title: 'Name',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: 'Email',\n    dataIndex: 'email',\n    key: 'email'\n  }, {\n    title: 'Role',\n    dataIndex: 'role',\n    key: 'role',\n    render: (role, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: record.isMainAdmin ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#722ed1',\n            fontWeight: 'bold'\n          },\n          children: \"Main Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '4px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"gold\",\n            children: \"Current User (Cannot be removed)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: role === 'super_admin' ? '#1890ff' : '#52c41a',\n          fontWeight: 'bold'\n        },\n        children: role\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Privileges',\n    dataIndex: 'privileges',\n    key: 'privileges',\n    render: (privileges, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: record.isMainAdmin ? /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"purple\",\n        style: {\n          fontWeight: 'bold'\n        },\n        children: \"All Privileges\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 13\n      }, this) : privileges.map((privilege, index) => /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: privilege\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 15\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Actions',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        disabled: record.isMainAdmin || record.id === (currentAdmin === null || currentAdmin === void 0 ? void 0 : currentAdmin.id),\n        children: \"Edit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"Are you sure you want to delete this admin?\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"Yes\",\n        cancelText: \"No\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 21\n          }, this),\n          disabled: record.isMainAdmin || record.id === (currentAdmin === null || currentAdmin === void 0 ? void 0 : currentAdmin.id),\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          children: \"Admin Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 21\n            }, this),\n            onClick: e => {\n              e.preventDefault(); // Prevent any default behavior\n              setEditingAdmin(null);\n              form.resetFields();\n              setIsModalVisible(true);\n            },\n            size: \"large\",\n            children: \"Add New Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"default\",\n            onClick: e => {\n              e.preventDefault();\n              // Refresh the admin list from the database\n              fetchAdmins();\n              message.success('Admin list refreshed from database');\n            },\n            size: \"large\",\n            children: \"Refresh Admin List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: admins,\n        rowKey: \"id\",\n        pagination: {\n          pageSize: 10\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingAdmin ? 'Edit Admin' : 'Add New Admin',\n      open: isModalVisible,\n      onCancel: e => {\n        e.preventDefault(); // Prevent any default behavior\n        setIsModalVisible(false);\n        form.resetFields();\n        setEditingAdmin(null);\n      },\n      maskClosable: false,\n      destroyOnClose: true,\n      footer: null,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: editingAdmin ? handleUpdateAdmin : handleCreateAdmin,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"Name\",\n          rules: [{\n            required: true,\n            message: 'Please input admin name!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"Enter admin name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          label: \"Email\",\n          rules: [{\n            required: true,\n            message: 'Please input admin email!'\n          }, {\n            type: 'email',\n            message: 'Please enter a valid email!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"Enter admin email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), !editingAdmin && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            rules: [{\n              required: true,\n              message: 'Please input admin password!'\n            }, {\n              min: 8,\n              message: 'Password must be at least 8 characters!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              placeholder: \"Enter admin password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"confirmPassword\",\n            label: \"Confirm Password\",\n            dependencies: ['password'],\n            rules: [{\n              required: true,\n              message: 'Please confirm admin password!'\n            }, ({\n              getFieldValue\n            }) => ({\n              validator(_, value) {\n                if (!value || getFieldValue('password') === value) {\n                  return Promise.resolve();\n                }\n                return Promise.reject(new Error('The two passwords do not match!'));\n              }\n            })],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              placeholder: \"Confirm admin password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"role\",\n          label: \"Role\",\n          rules: [{\n            required: true,\n            message: 'Please select admin role!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"Select admin role\",\n            children: [/*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"admin\",\n              children: \"Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select.Option, {\n              value: \"super_admin\",\n              children: \"Super Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                setIsModalVisible(false);\n                form.resetFields();\n                setEditingAdmin(null);\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingAdmin ? 'Update Admin' : 'Create Admin'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 333,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminManagement, \"pCXeQfgjiftV9CP//wxHEtbHKZA=\", false, function () {\n  return [useAuth, useNavigate, Form.useForm];\n});\n_c = AdminManagement;\nexport default AdminManagement;\nvar _c;\n$RefreshReg$(_c, \"AdminManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useAuth", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Space", "Card", "Typography", "Popconfirm", "Tag", "PlusOutlined", "EditOutlined", "DeleteOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "AdminManagement", "_s", "admin", "currentAdmin", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "navigate", "admins", "setAdmins", "isModalVisible", "setIsModalVisible", "editingAdmin", "setEditingAdmin", "form", "useForm", "error", "replace", "fetchAdmins", "console", "log", "originalSetItem", "localStorage", "setItem", "key", "value", "call", "token", "getItem", "response", "fetch", "process", "env", "REACT_APP_API_URL", "method", "headers", "ok", "Error", "status", "statusText", "data", "json", "handleCreateAdmin", "values", "email", "toLowerCase", "body", "JSON", "stringify", "name", "password", "role", "errorData", "newAdmin", "success", "resetFields", "includes", "handleEdit", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleUpdateAdmin", "id", "updatedAdmins", "map", "privileges", "handleDelete", "adminToDelete", "find", "columns", "title", "dataIndex", "render", "children", "style", "color", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "privilege", "index", "_", "type", "icon", "onClick", "disabled", "onConfirm", "okText", "cancelText", "danger", "className", "level", "e", "preventDefault", "size", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "open", "onCancel", "maskClosable", "destroyOnClose", "footer", "layout", "onFinish", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "min", "Password", "dependencies", "getFieldValue", "validator", "Promise", "resolve", "reject", "Option", "htmlType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport {\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Space,\n  Card,\n  Typography,\n  Popconfirm,\n  Tag\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\n\nconst { Title } = Typography;\n\ninterface Admin {\n  id: number;\n  name: string;\n  email: string;\n  role: string;\n  privileges: string[];\n  isMainAdmin: boolean;\n}\n\nconst AdminManagement: React.FC = () => {\n  const { admin: currentAdmin } = useAuth();\n  const isMainAdmin = currentAdmin?.isMainAdmin || false;\n  const navigate = useNavigate();\n  const [admins, setAdmins] = useState<Admin[]>([]);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingAdmin, setEditingAdmin] = useState<Admin | null>(null);\n  const [form] = Form.useForm();\n\n  // Redirect non-main admins away from this page\n  useEffect(() => {\n    if (!isMainAdmin) {\n      message.error('Access denied. Only the main admin can access this page.');\n      navigate('/admin/dashboard', { replace: true });\n    }\n  }, [isMainAdmin, navigate]);\n\n  useEffect(() => {\n    fetchAdmins();\n\n    // Debug: Log when component mounts\n    console.log('AdminManagement component mounted');\n\n    // Add event listener to log localStorage changes\n    const originalSetItem = localStorage.setItem;\n    localStorage.setItem = function(key, value) {\n      console.log(`localStorage.setItem called with key \"${key}\":`, value);\n      originalSetItem.call(this, key, value);\n    };\n\n    return () => {\n      // Restore original function when component unmounts\n      localStorage.setItem = originalSetItem;\n      console.log('AdminManagement component unmounted');\n    };\n  }, []);\n\n  const fetchAdmins = async () => {\n    try {\n      console.log('Fetching admins from API...');\n\n      // Get the admin token from localStorage\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        console.error('No admin token found');\n        message.error('Authentication error. Please log in again.');\n        return;\n      }\n\n      // Fetch admins from the API\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/all`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch admins: ${response.status} ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      console.log('Admins loaded from API:', data);\n      setAdmins(data);\n    } catch (error) {\n      console.error('Error in fetchAdmins:', error);\n      message.error('Failed to fetch admins');\n    }\n  };\n\n  const handleCreateAdmin = async (values: any) => {\n    try {\n      console.log('Creating admin with values:', values);\n\n      // Check if we're trying to create a main admin\n      if (values.email.toLowerCase() === '<EMAIL>') {\n        message.error('Cannot create another Main Admin account');\n        return;\n      }\n\n      // Get the admin token from localStorage\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        console.error('No admin token found');\n        message.error('Authentication error. Please log in again.');\n        return;\n      }\n\n      // Create admin through the API\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/create`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          name: values.name,\n          email: values.email,\n          password: values.password,\n          role: values.role\n        })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || `Failed to create admin: ${response.status} ${response.statusText}`);\n      }\n\n      const newAdmin = await response.json();\n      console.log('New admin created via API:', newAdmin);\n\n      // Refresh the admin list\n      fetchAdmins();\n\n      message.success('Admin created successfully');\n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      if (error instanceof Error && error.message.includes('Admin already exists')) {\n        message.error('An admin with this email already exists');\n      } else {\n        message.error('Failed to create admin');\n        console.error('Error creating admin:', error);\n      }\n    }\n  };\n\n  const handleEdit = (record: Admin) => {\n    setEditingAdmin(record);\n    form.setFieldsValue({\n      name: record.name,\n      email: record.email,\n      role: record.role,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleUpdateAdmin = async (values: any) => {\n    if (!editingAdmin) return;\n\n    try {\n      console.log('Updating admin with ID:', editingAdmin.id, 'Values:', values);\n\n      // Mock updating an admin\n      const updatedAdmins = admins.map(admin => {\n        if (admin.id === editingAdmin.id) {\n          return {\n            ...admin,\n            name: values.name,\n            email: values.email,\n            role: values.role,\n            privileges: values.role === 'super_admin' ? ['view', 'edit', 'delete'] : ['view', 'edit'],\n          };\n        }\n        return admin;\n      });\n\n      // Save to localStorage\n      localStorage.setItem('admins', JSON.stringify(updatedAdmins));\n\n      setAdmins(updatedAdmins);\n\n      message.success('Admin updated successfully');\n      setIsModalVisible(false);\n      form.resetFields();\n      setEditingAdmin(null);\n    } catch (error) {\n      message.error('Failed to update admin');\n      console.error('Error updating admin:', error);\n    }\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      console.log('Deleting admin with ID:', id);\n\n      // Check if this is the main admin\n      const adminToDelete = admins.find(admin => admin.id === id);\n      if (adminToDelete?.isMainAdmin) {\n        message.error('The main admin account cannot be deleted');\n        return;\n      }\n\n      // Get the admin token from localStorage\n      const token = localStorage.getItem('adminToken');\n      if (!token) {\n        console.error('No admin token found');\n        message.error('Authentication error. Please log in again.');\n        return;\n      }\n\n      // Delete admin through the API\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/admin/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || `Failed to delete admin: ${response.status} ${response.statusText}`);\n      }\n\n      // Refresh the admin list\n      fetchAdmins();\n\n      message.success('Admin deleted successfully');\n    } catch (error) {\n      message.error('Failed to delete admin');\n      console.error('Error deleting admin:', error);\n    }\n  };\n\n  const columns = [\n    {\n      title: 'Name',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: 'Email',\n      dataIndex: 'email',\n      key: 'email',\n    },\n    {\n      title: 'Role',\n      dataIndex: 'role',\n      key: 'role',\n      render: (role: string, record: Admin) => (\n        <div>\n          {record.isMainAdmin ? (\n            <>\n              <span style={{ color: '#722ed1', fontWeight: 'bold' }}>Main Admin</span>\n              <div style={{ marginTop: '4px' }}>\n                <Tag color=\"gold\">Current User (Cannot be removed)</Tag>\n              </div>\n            </>\n          ) : (\n            <span style={{\n              color: role === 'super_admin' ? '#1890ff' : '#52c41a',\n              fontWeight: 'bold'\n            }}>\n              {role}\n            </span>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: 'Privileges',\n      dataIndex: 'privileges',\n      key: 'privileges',\n      render: (privileges: string[], record: Admin) => (\n        <Space>\n          {record.isMainAdmin ? (\n            <Tag color=\"purple\" style={{ fontWeight: 'bold' }}>All Privileges</Tag>\n          ) : (\n            privileges.map((privilege, index) => (\n              <Tag key={index} color=\"blue\">\n                {privilege}\n              </Tag>\n            ))\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      render: (_: any, record: Admin) => (\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n            disabled={record.isMainAdmin || record.id === currentAdmin?.id}\n          >\n            Edit\n          </Button>\n          <Popconfirm\n            title=\"Are you sure you want to delete this admin?\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"Yes\"\n            cancelText=\"No\"\n          >\n            <Button\n              danger\n              icon={<DeleteOutlined />}\n              disabled={record.isMainAdmin || record.id === currentAdmin?.id}\n            >\n              Delete\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"p-6\">\n      <Card className=\"mb-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <Title level={2}>Admin Management</Title>\n          <div className=\"flex space-x-4\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={(e) => {\n                e.preventDefault(); // Prevent any default behavior\n                setEditingAdmin(null);\n                form.resetFields();\n                setIsModalVisible(true);\n              }}\n              size=\"large\"\n            >\n              Add New Admin\n            </Button>\n\n            <Button\n              type=\"default\"\n              onClick={(e) => {\n                e.preventDefault();\n                // Refresh the admin list from the database\n                fetchAdmins();\n                message.success('Admin list refreshed from database');\n              }}\n              size=\"large\"\n            >\n              Refresh Admin List\n            </Button>\n          </div>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={admins}\n          rowKey=\"id\"\n          pagination={{ pageSize: 10 }}\n        />\n      </Card>\n\n      <Modal\n        title={editingAdmin ? 'Edit Admin' : 'Add New Admin'}\n        open={isModalVisible}\n        onCancel={(e) => {\n          e.preventDefault(); // Prevent any default behavior\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingAdmin(null);\n        }}\n        maskClosable={false}\n        destroyOnClose={true}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={editingAdmin ? handleUpdateAdmin : handleCreateAdmin}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"Name\"\n            rules={[{ required: true, message: 'Please input admin name!' }]}\n          >\n            <Input placeholder=\"Enter admin name\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"email\"\n            label=\"Email\"\n            rules={[\n              { required: true, message: 'Please input admin email!' },\n              { type: 'email', message: 'Please enter a valid email!' }\n            ]}\n          >\n            <Input placeholder=\"Enter admin email\" />\n          </Form.Item>\n\n          {!editingAdmin && (\n            <>\n              <Form.Item\n                name=\"password\"\n                label=\"Password\"\n                rules={[\n                  { required: true, message: 'Please input admin password!' },\n                  { min: 8, message: 'Password must be at least 8 characters!' }\n                ]}\n              >\n                <Input.Password placeholder=\"Enter admin password\" />\n              </Form.Item>\n\n              <Form.Item\n                name=\"confirmPassword\"\n                label=\"Confirm Password\"\n                dependencies={['password']}\n                rules={[\n                  { required: true, message: 'Please confirm admin password!' },\n                  ({ getFieldValue }) => ({\n                    validator(_, value) {\n                      if (!value || getFieldValue('password') === value) {\n                        return Promise.resolve();\n                      }\n                      return Promise.reject(new Error('The two passwords do not match!'));\n                    },\n                  }),\n                ]}\n              >\n                <Input.Password placeholder=\"Confirm admin password\" />\n              </Form.Item>\n            </>\n          )}\n\n          <Form.Item\n            name=\"role\"\n            label=\"Role\"\n            rules={[{ required: true, message: 'Please select admin role!' }]}\n          >\n            <Select placeholder=\"Select admin role\">\n              <Select.Option value=\"admin\">Admin</Select.Option>\n              <Select.Option value=\"super_admin\">Super Admin</Select.Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item>\n            <div className=\"flex justify-end space-x-4\">\n              <Button onClick={() => {\n                setIsModalVisible(false);\n                form.resetFields();\n                setEditingAdmin(null);\n              }}>\n                Cancel\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingAdmin ? 'Update Admin' : 'Create Admin'}\n              </Button>\n            </div>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AdminManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,UAAU,EACVC,GAAG,QACE,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/E,MAAM;EAAEC;AAAM,CAAC,GAAGV,UAAU;AAW5B,MAAMW,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC,KAAK,EAAEC;EAAa,CAAC,GAAGxB,OAAO,CAAC,CAAC;EACzC,MAAMyB,WAAW,GAAG,CAAAD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,WAAW,KAAI,KAAK;EACtD,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAe,IAAI,CAAC;EACpE,MAAM,CAACoC,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;;EAE7B;EACApC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2B,WAAW,EAAE;MAChBlB,OAAO,CAAC4B,KAAK,CAAC,0DAA0D,CAAC;MACzET,QAAQ,CAAC,kBAAkB,EAAE;QAAEU,OAAO,EAAE;MAAK,CAAC,CAAC;IACjD;EACF,CAAC,EAAE,CAACX,WAAW,EAAEC,QAAQ,CAAC,CAAC;EAE3B5B,SAAS,CAAC,MAAM;IACduC,WAAW,CAAC,CAAC;;IAEb;IACAC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;IAEhD;IACA,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO;IAC5CD,YAAY,CAACC,OAAO,GAAG,UAASC,GAAG,EAAEC,KAAK,EAAE;MAC1CN,OAAO,CAACC,GAAG,CAAC,yCAAyCI,GAAG,IAAI,EAAEC,KAAK,CAAC;MACpEJ,eAAe,CAACK,IAAI,CAAC,IAAI,EAAEF,GAAG,EAAEC,KAAK,CAAC;IACxC,CAAC;IAED,OAAO,MAAM;MACX;MACAH,YAAY,CAACC,OAAO,GAAGF,eAAe;MACtCF,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;MAE1C;MACA,MAAMO,KAAK,GAAGL,YAAY,CAACM,OAAO,CAAC,YAAY,CAAC;MAChD,IAAI,CAACD,KAAK,EAAE;QACVR,OAAO,CAACH,KAAK,CAAC,sBAAsB,CAAC;QACrC5B,OAAO,CAAC4B,KAAK,CAAC,4CAA4C,CAAC;QAC3D;MACF;;MAEA;MACA,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,gBAAgB,EAAE;QACxGC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUR,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACE,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,2BAA2BR,QAAQ,CAACS,MAAM,IAAIT,QAAQ,CAACU,UAAU,EAAE,CAAC;MACtF;MAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;MAClCtB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoB,IAAI,CAAC;MAC5C/B,SAAS,CAAC+B,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C5B,OAAO,CAAC4B,KAAK,CAAC,wBAAwB,CAAC;IACzC;EACF,CAAC;EAED,MAAM0B,iBAAiB,GAAG,MAAOC,MAAW,IAAK;IAC/C,IAAI;MACFxB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEuB,MAAM,CAAC;;MAElD;MACA,IAAIA,MAAM,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC,KAAK,oBAAoB,EAAE;QACvDzD,OAAO,CAAC4B,KAAK,CAAC,0CAA0C,CAAC;QACzD;MACF;;MAEA;MACA,MAAMW,KAAK,GAAGL,YAAY,CAACM,OAAO,CAAC,YAAY,CAAC;MAChD,IAAI,CAACD,KAAK,EAAE;QACVR,OAAO,CAACH,KAAK,CAAC,sBAAsB,CAAC;QACrC5B,OAAO,CAAC4B,KAAK,CAAC,4CAA4C,CAAC;QAC3D;MACF;;MAEA;MACA,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,mBAAmB,EAAE;QAC3GC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUR,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDmB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,IAAI,EAAEN,MAAM,CAACM,IAAI;UACjBL,KAAK,EAAED,MAAM,CAACC,KAAK;UACnBM,QAAQ,EAAEP,MAAM,CAACO,QAAQ;UACzBC,IAAI,EAAER,MAAM,CAACQ;QACf,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACtB,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMgB,SAAS,GAAG,MAAMvB,QAAQ,CAACY,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIJ,KAAK,CAACe,SAAS,CAAChE,OAAO,IAAI,2BAA2ByC,QAAQ,CAACS,MAAM,IAAIT,QAAQ,CAACU,UAAU,EAAE,CAAC;MAC3G;MAEA,MAAMc,QAAQ,GAAG,MAAMxB,QAAQ,CAACY,IAAI,CAAC,CAAC;MACtCtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEiC,QAAQ,CAAC;;MAEnD;MACAnC,WAAW,CAAC,CAAC;MAEb9B,OAAO,CAACkE,OAAO,CAAC,4BAA4B,CAAC;MAC7C3C,iBAAiB,CAAC,KAAK,CAAC;MACxBG,IAAI,CAACyC,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYqB,KAAK,IAAIrB,KAAK,CAAC5B,OAAO,CAACoE,QAAQ,CAAC,sBAAsB,CAAC,EAAE;QAC5EpE,OAAO,CAAC4B,KAAK,CAAC,yCAAyC,CAAC;MAC1D,CAAC,MAAM;QACL5B,OAAO,CAAC4B,KAAK,CAAC,wBAAwB,CAAC;QACvCG,OAAO,CAACH,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAMyC,UAAU,GAAIC,MAAa,IAAK;IACpC7C,eAAe,CAAC6C,MAAM,CAAC;IACvB5C,IAAI,CAAC6C,cAAc,CAAC;MAClBV,IAAI,EAAES,MAAM,CAACT,IAAI;MACjBL,KAAK,EAAEc,MAAM,CAACd,KAAK;MACnBO,IAAI,EAAEO,MAAM,CAACP;IACf,CAAC,CAAC;IACFxC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMiD,iBAAiB,GAAG,MAAOjB,MAAW,IAAK;IAC/C,IAAI,CAAC/B,YAAY,EAAE;IAEnB,IAAI;MACFO,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAER,YAAY,CAACiD,EAAE,EAAE,SAAS,EAAElB,MAAM,CAAC;;MAE1E;MACA,MAAMmB,aAAa,GAAGtD,MAAM,CAACuD,GAAG,CAAC3D,KAAK,IAAI;QACxC,IAAIA,KAAK,CAACyD,EAAE,KAAKjD,YAAY,CAACiD,EAAE,EAAE;UAChC,OAAO;YACL,GAAGzD,KAAK;YACR6C,IAAI,EAAEN,MAAM,CAACM,IAAI;YACjBL,KAAK,EAAED,MAAM,CAACC,KAAK;YACnBO,IAAI,EAAER,MAAM,CAACQ,IAAI;YACjBa,UAAU,EAAErB,MAAM,CAACQ,IAAI,KAAK,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM;UAC1F,CAAC;QACH;QACA,OAAO/C,KAAK;MACd,CAAC,CAAC;;MAEF;MACAkB,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEwB,IAAI,CAACC,SAAS,CAACc,aAAa,CAAC,CAAC;MAE7DrD,SAAS,CAACqD,aAAa,CAAC;MAExB1E,OAAO,CAACkE,OAAO,CAAC,4BAA4B,CAAC;MAC7C3C,iBAAiB,CAAC,KAAK,CAAC;MACxBG,IAAI,CAACyC,WAAW,CAAC,CAAC;MAClB1C,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,wBAAwB,CAAC;MACvCG,OAAO,CAACH,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMiD,YAAY,GAAG,MAAOJ,EAAU,IAAK;IACzC,IAAI;MACF1C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEyC,EAAE,CAAC;;MAE1C;MACA,MAAMK,aAAa,GAAG1D,MAAM,CAAC2D,IAAI,CAAC/D,KAAK,IAAIA,KAAK,CAACyD,EAAE,KAAKA,EAAE,CAAC;MAC3D,IAAIK,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAE5D,WAAW,EAAE;QAC9BlB,OAAO,CAAC4B,KAAK,CAAC,0CAA0C,CAAC;QACzD;MACF;;MAEA;MACA,MAAMW,KAAK,GAAGL,YAAY,CAACM,OAAO,CAAC,YAAY,CAAC;MAChD,IAAI,CAACD,KAAK,EAAE;QACVR,OAAO,CAACH,KAAK,CAAC,sBAAsB,CAAC;QACrC5B,OAAO,CAAC4B,KAAK,CAAC,4CAA4C,CAAC;QAC3D;MACF;;MAEA;MACA,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,cAAc4B,EAAE,EAAE,EAAE;QAC1G3B,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUR,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACE,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMgB,SAAS,GAAG,MAAMvB,QAAQ,CAACY,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIJ,KAAK,CAACe,SAAS,CAAChE,OAAO,IAAI,2BAA2ByC,QAAQ,CAACS,MAAM,IAAIT,QAAQ,CAACU,UAAU,EAAE,CAAC;MAC3G;;MAEA;MACArB,WAAW,CAAC,CAAC;MAEb9B,OAAO,CAACkE,OAAO,CAAC,4BAA4B,CAAC;IAC/C,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,wBAAwB,CAAC;MACvCG,OAAO,CAACH,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMoD,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjB9C,GAAG,EAAE;EACP,CAAC,EACD;IACE6C,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,OAAO;IAClB9C,GAAG,EAAE;EACP,CAAC,EACD;IACE6C,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjB9C,GAAG,EAAE,MAAM;IACX+C,MAAM,EAAEA,CAACpB,IAAY,EAAEO,MAAa,kBAClC5D,OAAA;MAAA0E,QAAA,EACGd,MAAM,CAACpD,WAAW,gBACjBR,OAAA,CAAAE,SAAA;QAAAwE,QAAA,gBACE1E,OAAA;UAAM2E,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxEjF,OAAA;UAAK2E,KAAK,EAAE;YAAEO,SAAS,EAAE;UAAM,CAAE;UAAAR,QAAA,eAC/B1E,OAAA,CAACL,GAAG;YAACiF,KAAK,EAAC,MAAM;YAAAF,QAAA,EAAC;UAAgC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA,eACN,CAAC,gBAEHjF,OAAA;QAAM2E,KAAK,EAAE;UACXC,KAAK,EAAEvB,IAAI,KAAK,aAAa,GAAG,SAAS,GAAG,SAAS;UACrDwB,UAAU,EAAE;QACd,CAAE;QAAAH,QAAA,EACCrB;MAAI;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,EACD;IACEV,KAAK,EAAE,YAAY;IACnBC,SAAS,EAAE,YAAY;IACvB9C,GAAG,EAAE,YAAY;IACjB+C,MAAM,EAAEA,CAACP,UAAoB,EAAEN,MAAa,kBAC1C5D,OAAA,CAACT,KAAK;MAAAmF,QAAA,EACHd,MAAM,CAACpD,WAAW,gBACjBR,OAAA,CAACL,GAAG;QAACiF,KAAK,EAAC,QAAQ;QAACD,KAAK,EAAE;UAAEE,UAAU,EAAE;QAAO,CAAE;QAAAH,QAAA,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GAEvEf,UAAU,CAACD,GAAG,CAAC,CAACkB,SAAS,EAAEC,KAAK,kBAC9BpF,OAAA,CAACL,GAAG;QAAaiF,KAAK,EAAC,MAAM;QAAAF,QAAA,EAC1BS;MAAS,GADFC,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,SAAS;IAChB7C,GAAG,EAAE,SAAS;IACd+C,MAAM,EAAEA,CAACY,CAAM,EAAEzB,MAAa,kBAC5B5D,OAAA,CAACT,KAAK;MAAAmF,QAAA,gBACJ1E,OAAA,CAACf,MAAM;QACLqG,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEvF,OAAA,CAACH,YAAY;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBO,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAACC,MAAM,CAAE;QAClC6B,QAAQ,EAAE7B,MAAM,CAACpD,WAAW,IAAIoD,MAAM,CAACG,EAAE,MAAKxD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwD,EAAE,CAAC;QAAAW,QAAA,EAChE;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjF,OAAA,CAACN,UAAU;QACT6E,KAAK,EAAC,6CAA6C;QACnDmB,SAAS,EAAEA,CAAA,KAAMvB,YAAY,CAACP,MAAM,CAACG,EAAE,CAAE;QACzC4B,MAAM,EAAC,KAAK;QACZC,UAAU,EAAC,IAAI;QAAAlB,QAAA,eAEf1E,OAAA,CAACf,MAAM;UACL4G,MAAM;UACNN,IAAI,eAAEvF,OAAA,CAACF,cAAc;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBQ,QAAQ,EAAE7B,MAAM,CAACpD,WAAW,IAAIoD,MAAM,CAACG,EAAE,MAAKxD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwD,EAAE,CAAC;UAAAW,QAAA,EAChE;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEjF,OAAA;IAAK8F,SAAS,EAAC,KAAK;IAAApB,QAAA,gBAClB1E,OAAA,CAACR,IAAI;MAACsG,SAAS,EAAC,MAAM;MAAApB,QAAA,gBACpB1E,OAAA;QAAK8F,SAAS,EAAC,wCAAwC;QAAApB,QAAA,gBACrD1E,OAAA,CAACG,KAAK;UAAC4F,KAAK,EAAE,CAAE;UAAArB,QAAA,EAAC;QAAgB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzCjF,OAAA;UAAK8F,SAAS,EAAC,gBAAgB;UAAApB,QAAA,gBAC7B1E,OAAA,CAACf,MAAM;YACLqG,IAAI,EAAC,SAAS;YACdC,IAAI,eAAEvF,OAAA,CAACJ,YAAY;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBO,OAAO,EAAGQ,CAAC,IAAK;cACdA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;cACpBlF,eAAe,CAAC,IAAI,CAAC;cACrBC,IAAI,CAACyC,WAAW,CAAC,CAAC;cAClB5C,iBAAiB,CAAC,IAAI,CAAC;YACzB,CAAE;YACFqF,IAAI,EAAC,OAAO;YAAAxB,QAAA,EACb;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjF,OAAA,CAACf,MAAM;YACLqG,IAAI,EAAC,SAAS;YACdE,OAAO,EAAGQ,CAAC,IAAK;cACdA,CAAC,CAACC,cAAc,CAAC,CAAC;cAClB;cACA7E,WAAW,CAAC,CAAC;cACb9B,OAAO,CAACkE,OAAO,CAAC,oCAAoC,CAAC;YACvD,CAAE;YACF0C,IAAI,EAAC,OAAO;YAAAxB,QAAA,EACb;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjF,OAAA,CAAChB,KAAK;QACJsF,OAAO,EAAEA,OAAQ;QACjB6B,UAAU,EAAEzF,MAAO;QACnB0F,MAAM,EAAC,IAAI;QACXC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPjF,OAAA,CAACd,KAAK;MACJqF,KAAK,EAAEzD,YAAY,GAAG,YAAY,GAAG,eAAgB;MACrDyF,IAAI,EAAE3F,cAAe;MACrB4F,QAAQ,EAAGR,CAAC,IAAK;QACfA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;QACpBpF,iBAAiB,CAAC,KAAK,CAAC;QACxBG,IAAI,CAACyC,WAAW,CAAC,CAAC;QAClB1C,eAAe,CAAC,IAAI,CAAC;MACvB,CAAE;MACF0F,YAAY,EAAE,KAAM;MACpBC,cAAc,EAAE,IAAK;MACrBC,MAAM,EAAE,IAAK;MAAAjC,QAAA,eAEb1E,OAAA,CAACb,IAAI;QACH6B,IAAI,EAAEA,IAAK;QACX4F,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE/F,YAAY,GAAGgD,iBAAiB,GAAGlB,iBAAkB;QAAA8B,QAAA,gBAE/D1E,OAAA,CAACb,IAAI,CAAC2H,IAAI;UACR3D,IAAI,EAAC,MAAM;UACX4D,KAAK,EAAC,MAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE3H,OAAO,EAAE;UAA2B,CAAC,CAAE;UAAAoF,QAAA,eAEjE1E,OAAA,CAACZ,KAAK;YAAC8H,WAAW,EAAC;UAAkB;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAEZjF,OAAA,CAACb,IAAI,CAAC2H,IAAI;UACR3D,IAAI,EAAC,OAAO;UACZ4D,KAAK,EAAC,OAAO;UACbC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE3H,OAAO,EAAE;UAA4B,CAAC,EACxD;YAAEgG,IAAI,EAAE,OAAO;YAAEhG,OAAO,EAAE;UAA8B,CAAC,CACzD;UAAAoF,QAAA,eAEF1E,OAAA,CAACZ,KAAK;YAAC8H,WAAW,EAAC;UAAmB;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,EAEX,CAACnE,YAAY,iBACZd,OAAA,CAAAE,SAAA;UAAAwE,QAAA,gBACE1E,OAAA,CAACb,IAAI,CAAC2H,IAAI;YACR3D,IAAI,EAAC,UAAU;YACf4D,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE3H,OAAO,EAAE;YAA+B,CAAC,EAC3D;cAAE6H,GAAG,EAAE,CAAC;cAAE7H,OAAO,EAAE;YAA0C,CAAC,CAC9D;YAAAoF,QAAA,eAEF1E,OAAA,CAACZ,KAAK,CAACgI,QAAQ;cAACF,WAAW,EAAC;YAAsB;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAEZjF,OAAA,CAACb,IAAI,CAAC2H,IAAI;YACR3D,IAAI,EAAC,iBAAiB;YACtB4D,KAAK,EAAC,kBAAkB;YACxBM,YAAY,EAAE,CAAC,UAAU,CAAE;YAC3BL,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE3H,OAAO,EAAE;YAAiC,CAAC,EAC7D,CAAC;cAAEgI;YAAc,CAAC,MAAM;cACtBC,SAASA,CAAClC,CAAC,EAAE1D,KAAK,EAAE;gBAClB,IAAI,CAACA,KAAK,IAAI2F,aAAa,CAAC,UAAU,CAAC,KAAK3F,KAAK,EAAE;kBACjD,OAAO6F,OAAO,CAACC,OAAO,CAAC,CAAC;gBAC1B;gBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAInF,KAAK,CAAC,iCAAiC,CAAC,CAAC;cACrE;YACF,CAAC,CAAC,CACF;YAAAmC,QAAA,eAEF1E,OAAA,CAACZ,KAAK,CAACgI,QAAQ;cAACF,WAAW,EAAC;YAAwB;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA,eACZ,CACH,eAEDjF,OAAA,CAACb,IAAI,CAAC2H,IAAI;UACR3D,IAAI,EAAC,MAAM;UACX4D,KAAK,EAAC,MAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE3H,OAAO,EAAE;UAA4B,CAAC,CAAE;UAAAoF,QAAA,eAElE1E,OAAA,CAACX,MAAM;YAAC6H,WAAW,EAAC,mBAAmB;YAAAxC,QAAA,gBACrC1E,OAAA,CAACX,MAAM,CAACsI,MAAM;cAAChG,KAAK,EAAC,OAAO;cAAA+C,QAAA,EAAC;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAClDjF,OAAA,CAACX,MAAM,CAACsI,MAAM;cAAChG,KAAK,EAAC,aAAa;cAAA+C,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZjF,OAAA,CAACb,IAAI,CAAC2H,IAAI;UAAApC,QAAA,eACR1E,OAAA;YAAK8F,SAAS,EAAC,4BAA4B;YAAApB,QAAA,gBACzC1E,OAAA,CAACf,MAAM;cAACuG,OAAO,EAAEA,CAAA,KAAM;gBACrB3E,iBAAiB,CAAC,KAAK,CAAC;gBACxBG,IAAI,CAACyC,WAAW,CAAC,CAAC;gBAClB1C,eAAe,CAAC,IAAI,CAAC;cACvB,CAAE;cAAA2D,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjF,OAAA,CAACf,MAAM;cAACqG,IAAI,EAAC,SAAS;cAACsC,QAAQ,EAAC,QAAQ;cAAAlD,QAAA,EACrC5D,YAAY,GAAG,cAAc,GAAG;YAAc;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5E,EAAA,CA5bID,eAAyB;EAAA,QACGrB,OAAO,EAEtBD,WAAW,EAIbK,IAAI,CAAC8B,OAAO;AAAA;AAAA4G,EAAA,GAPvBzH,eAAyB;AA8b/B,eAAeA,eAAe;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}