import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { query } from '../config/database';

// ENTERPRISE-GRADE PASSWORD POLICY CONFIGURATION
const DEFAULT_MIN_LENGTH = 12; // Increased from 8 for better security
const DEFAULT_REQUIRE_UPPERCASE = true;
const DEFAULT_REQUIRE_LOWERCASE = true;
const DEFAULT_REQUIRE_NUMBERS = true;
const DEFAULT_REQUIRE_SYMBOLS = true;
const DEFAULT_PASSWORD_HISTORY = 5; // Increased from 3
const DEFAULT_PASSWORD_EXPIRY_DAYS = 90;
const DEFAULT_MAX_REPEATED_CHARS = 3;
const DEFAULT_MIN_UNIQUE_CHARS = 8;

// Get password policy from environment variables or use defaults
const MIN_PASSWORD_LENGTH = parseInt(process.env.MIN_PASSWORD_LENGTH || DEFAULT_MIN_LENGTH.toString());
const REQUIRE_UPPERCASE = process.env.REQUIRE_UPPERCASE !== 'false';
const REQUIRE_LOWERCASE = process.env.REQUIRE_LOWERCASE !== 'false';
const REQUIRE_NUMBERS = process.env.REQUIRE_NUMBERS !== 'false';
const REQUIRE_SYMBOLS = process.env.REQUIRE_SYMBOLS !== 'false';
const PASSWORD_HISTORY_COUNT = parseInt(process.env.PASSWORD_HISTORY_COUNT || DEFAULT_PASSWORD_HISTORY.toString());
const PASSWORD_EXPIRY_DAYS = parseInt(process.env.PASSWORD_EXPIRY_DAYS || DEFAULT_PASSWORD_EXPIRY_DAYS.toString());
const MAX_REPEATED_CHARS = parseInt(process.env.MAX_REPEATED_CHARS || DEFAULT_MAX_REPEATED_CHARS.toString());
const MIN_UNIQUE_CHARS = parseInt(process.env.MIN_UNIQUE_CHARS || DEFAULT_MIN_UNIQUE_CHARS.toString());

// Common weak passwords to reject
const WEAK_PASSWORDS = [
  'password', 'password123', 'admin123', 'admin', '123456', '123456789',
  'qwerty', 'abc123', 'password1', 'admin1', 'root', 'toor', 'pass',
  'test', 'guest', 'user', 'demo', 'sample', 'default', 'changeme',
  'welcome', 'login', 'secret', 'master', 'super', 'administrator'
];

// Common patterns to reject
const WEAK_PATTERNS = [
  /^(.)\1{2,}$/, // All same character
  /^(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i, // Sequential
  /^(qwe|asd|zxc|qaz|wsx|edc)/i, // Keyboard patterns
];

export interface PasswordValidationResult {
  valid: boolean;
  message: string;
  score?: number; // Password strength score (0-100)
  suggestions?: string[];
}

/**
 * ENTERPRISE-GRADE PASSWORD VALIDATION
 * Validates password strength according to industry best practices
 */
export const validatePasswordStrength = (password: string): PasswordValidationResult => {
  const suggestions: string[] = [];
  let score = 0;

  // Check password length
  if (password.length < MIN_PASSWORD_LENGTH) {
    return {
      valid: false,
      message: `Password must be at least ${MIN_PASSWORD_LENGTH} characters long`,
      score: 0,
      suggestions: [`Use at least ${MIN_PASSWORD_LENGTH} characters`]
    };
  }
  score += Math.min(password.length * 2, 20); // Up to 20 points for length

  // Check for weak passwords
  if (WEAK_PASSWORDS.includes(password.toLowerCase())) {
    return {
      valid: false,
      message: 'This password is too common and easily guessable',
      score: 0,
      suggestions: ['Use a unique password that is not commonly used']
    };
  }

  // Check for weak patterns
  for (const pattern of WEAK_PATTERNS) {
    if (pattern.test(password)) {
      return {
        valid: false,
        message: 'Password contains predictable patterns',
        score: 0,
        suggestions: ['Avoid sequential characters or keyboard patterns']
      };
    }
  }

  // Check for uppercase letters
  if (REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one uppercase letter',
      score: Math.max(score - 10, 0),
      suggestions: ['Add at least one uppercase letter (A-Z)']
    };
  }
  if (/[A-Z]/.test(password)) score += 10;

  // Check for lowercase letters
  if (REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one lowercase letter',
      score: Math.max(score - 10, 0),
      suggestions: ['Add at least one lowercase letter (a-z)']
    };
  }
  if (/[a-z]/.test(password)) score += 10;

  // Check for numbers
  if (REQUIRE_NUMBERS && !/\d/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one number',
      score: Math.max(score - 10, 0),
      suggestions: ['Add at least one number (0-9)']
    };
  }
  if (/\d/.test(password)) score += 10;

  // Check for symbols
  if (REQUIRE_SYMBOLS && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one special character',
      score: Math.max(score - 10, 0),
      suggestions: ['Add at least one special character (!@#$%^&*...)']
    };
  }
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 15;

  // Check for repeated characters
  const repeatedChars = password.match(/(.)\1{2,}/g);
  if (repeatedChars && repeatedChars.some(match => match.length > MAX_REPEATED_CHARS)) {
    return {
      valid: false,
      message: `Password cannot have more than ${MAX_REPEATED_CHARS} consecutive identical characters`,
      score: Math.max(score - 15, 0),
      suggestions: ['Avoid repeating the same character multiple times']
    };
  }

  // Check for unique characters
  const uniqueChars = new Set(password).size;
  if (uniqueChars < MIN_UNIQUE_CHARS) {
    return {
      valid: false,
      message: `Password must contain at least ${MIN_UNIQUE_CHARS} unique characters`,
      score: Math.max(score - 10, 0),
      suggestions: [`Use at least ${MIN_UNIQUE_CHARS} different characters`]
    };
  }
  score += Math.min(uniqueChars * 2, 20); // Up to 20 points for character diversity

  // Additional scoring for complexity
  if (password.length >= 16) score += 10; // Bonus for very long passwords
  if (/[A-Z].*[A-Z]/.test(password)) score += 5; // Multiple uppercase
  if (/[a-z].*[a-z]/.test(password)) score += 5; // Multiple lowercase
  if (/\d.*\d/.test(password)) score += 5; // Multiple numbers
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?].*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 10; // Multiple symbols

  // Cap the score at 100
  score = Math.min(score, 100);

  // Add suggestions for improvement
  if (score < 80) {
    if (password.length < 16) suggestions.push('Consider using a longer password (16+ characters)');
    if (!/[A-Z].*[A-Z]/.test(password)) suggestions.push('Use multiple uppercase letters');
    if (!/\d.*\d/.test(password)) suggestions.push('Use multiple numbers');
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?].*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      suggestions.push('Use multiple special characters');
    }
  }

  return {
    valid: true,
    message: score >= 80 ? 'Strong password' : score >= 60 ? 'Good password' : 'Acceptable password',
    score,
    suggestions: suggestions.length > 0 ? suggestions : undefined
  };
};

/**
 * Check if password is in user's password history
 */
export const isPasswordInHistory = async (userId: number, password: string, isAdmin: boolean = false): Promise<boolean> => {
  try {
    const tableName = isAdmin ? 'admins' : 'users';
    const result = await query(`
      SELECT password_hash FROM password_history 
      WHERE user_id = $1 AND table_name = $2
      ORDER BY created_at DESC 
      LIMIT $3
    `, [userId, tableName, PASSWORD_HISTORY_COUNT]);

    // Check if password matches any in history
    for (const row of result.rows) {
      const isMatch = await bcrypt.compare(password, row.password_hash);
      if (isMatch) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking password history:', error);
    return false;
  }
};

/**
 * Add password to user's password history
 */
export const addPasswordToHistory = async (userId: number, passwordHash: string, isAdmin: boolean = false): Promise<void> => {
  try {
    const tableName = isAdmin ? 'admins' : 'users';
    
    // Add new password to history
    await query(`
      INSERT INTO password_history (user_id, password_hash, table_name, created_at)
      VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
    `, [userId, passwordHash, tableName]);

    // Clean up old password history entries (keep only the latest ones)
    await query(`
      DELETE FROM password_history 
      WHERE user_id = $1 AND table_name = $2 AND id NOT IN (
        SELECT id FROM password_history 
        WHERE user_id = $1 AND table_name = $2
        ORDER BY created_at DESC 
        LIMIT $3
      )
    `, [userId, tableName, PASSWORD_HISTORY_COUNT]);
  } catch (error) {
    console.error('Error adding password to history:', error);
  }
};

/**
 * Generate a cryptographically secure password
 */
export const generateSecurePassword = (length: number = 16): string => {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  let password = '';
  
  // Ensure at least one character from each required category
  if (REQUIRE_UPPERCASE) password += uppercase[crypto.randomInt(uppercase.length)];
  if (REQUIRE_LOWERCASE) password += lowercase[crypto.randomInt(lowercase.length)];
  if (REQUIRE_NUMBERS) password += numbers[crypto.randomInt(numbers.length)];
  if (REQUIRE_SYMBOLS) password += symbols[crypto.randomInt(symbols.length)];
  
  // Fill the rest with random characters from all categories
  const allChars = uppercase + lowercase + numbers + symbols;
  for (let i = password.length; i < length; i++) {
    password += allChars[crypto.randomInt(allChars.length)];
  }
  
  // Shuffle the password to avoid predictable patterns
  return password.split('').sort(() => crypto.randomInt(3) - 1).join('');
};

/**
 * Check if user's password has expired
 */
export const hasPasswordExpired = async (userId: number, isAdmin: boolean = false): Promise<boolean> => {
  try {
    const tableName = isAdmin ? 'admins' : 'users';
    const result = await query(`
      SELECT password_updated_at FROM ${tableName} WHERE id = $1
    `, [userId]);

    if (result.rows.length === 0 || !result.rows[0].password_updated_at) {
      return false;
    }

    const passwordUpdatedAt = new Date(result.rows[0].password_updated_at);
    const expiryDate = new Date(passwordUpdatedAt);
    expiryDate.setDate(expiryDate.getDate() + PASSWORD_EXPIRY_DAYS);

    return new Date() > expiryDate;
  } catch (error) {
    console.error('Error checking password expiry:', error);
    return false;
  }
};

/**
 * Get days until password expires
 */
export const getDaysUntilPasswordExpires = async (userId: number, isAdmin: boolean = false): Promise<number | null> => {
  try {
    const tableName = isAdmin ? 'admins' : 'users';
    const result = await query(`
      SELECT password_updated_at FROM ${tableName} WHERE id = $1
    `, [userId]);

    if (result.rows.length === 0 || !result.rows[0].password_updated_at) {
      return null;
    }

    const passwordUpdatedAt = new Date(result.rows[0].password_updated_at);
    const expiryDate = new Date(passwordUpdatedAt);
    expiryDate.setDate(expiryDate.getDate() + PASSWORD_EXPIRY_DAYS);

    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  } catch (error) {
    console.error('Error calculating days until password expires:', error);
    return null;
  }
};

export const getPasswordPolicyInfo = () => ({
  minLength: MIN_PASSWORD_LENGTH,
  requireUppercase: REQUIRE_UPPERCASE,
  requireLowercase: REQUIRE_LOWERCASE,
  requireNumbers: REQUIRE_NUMBERS,
  requireSymbols: REQUIRE_SYMBOLS,
  passwordHistoryCount: PASSWORD_HISTORY_COUNT,
  passwordExpiryDays: PASSWORD_EXPIRY_DAYS,
  maxRepeatedChars: MAX_REPEATED_CHARS,
  minUniqueChars: MIN_UNIQUE_CHARS
});
