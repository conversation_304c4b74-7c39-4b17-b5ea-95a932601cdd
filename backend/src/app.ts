import express from 'express';
import cors from 'cors';
import scholarshipsRouter from './routes/scholarships';
import messagesRouter from './routes/messages';
import newsletterRouter from './routes/newsletter';
import authRouter from './routes/auth.new';
import securityDashboardRouter from './routes/security.dashboard.routes';

const app = express();

app.use(cors());
app.use(express.json());

// API Routes
app.use('/api/scholarships', scholarshipsRouter);
app.use('/api/messages', messagesRouter);
app.use('/api/newsletter', newsletterRouter);
app.use('/api/auth', authRouter);
app.use('/api/security', securityDashboardRouter);

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

export default app; 