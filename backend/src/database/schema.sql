-- MaBourse Database Schema for PostgreSQL
-- This file contains the complete database schema for the MaBourse application

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    failed_login_attempts INTEGER DEFAULT 0,
    lock_until TIM<PERSON><PERSON><PERSON>,
    reset_password_token VARCHAR(255),
    reset_password_expires TIM<PERSON><PERSON><PERSON>,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    password_updated_at TIMESTAMP,
    password_expires_at TIMESTAMP,
    must_change_password BOOLEAN DEFAULT FALSE
);

-- Admins table
CREATE TABLE IF NOT EXISTS admins (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) <PERSON>IQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'admin',
    privileges TEXT, -- JSON string of privileges
    is_main_admin BOOLEAN DEFAULT FALSE,
    reset_password_token VARCHAR(255),
    reset_password_expires TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    lock_until TIMESTAMP,
    last_login TIMESTAMP,
    two_factor_secret VARCHAR(255),
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_temp_secret VARCHAR(255),
    two_factor_backup_codes TEXT, -- JSON array of backup codes
    password_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    password_expires_at TIMESTAMP,
    must_change_password BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Scholarships table
CREATE TABLE IF NOT EXISTS scholarships (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    level VARCHAR(100),
    country VARCHAR(100),
    deadline TIMESTAMP NOT NULL,
    is_open BOOLEAN DEFAULT TRUE,
    thumbnail VARCHAR(500),
    coverage TEXT,
    financial_benefits_summary TEXT,
    eligibility_summary TEXT,
    scholarship_link VARCHAR(500),
    youtube_link VARCHAR(500),
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Newsletter subscriptions table
CREATE TABLE IF NOT EXISTS newsletter_subscriptions (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enhanced security events table
CREATE TABLE IF NOT EXISTS security_events (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    user_id INTEGER,
    admin_id INTEGER,
    email VARCHAR(255),
    ip VARCHAR(45) NOT NULL,
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    details JSONB, -- Changed to JSONB for better querying
    severity VARCHAR(20) DEFAULT 'info',
    session_id VARCHAR(255),
    request_id VARCHAR(255),
    geolocation JSONB, -- Store IP geolocation data
    device_fingerprint VARCHAR(255),
    risk_score INTEGER DEFAULT 0, -- 0-100 risk assessment
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by INTEGER,
    resolved_at TIMESTAMP,
    resolution_notes TEXT
);

-- Login attempts tracking table
CREATE TABLE IF NOT EXISTS login_attempts (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    ip VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason VARCHAR(100),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    geolocation JSONB,
    device_fingerprint VARCHAR(255),
    session_id VARCHAR(255)
);

-- Device tracking table
CREATE TABLE IF NOT EXISTS trusted_devices (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    admin_id INTEGER,
    device_fingerprint VARCHAR(255) NOT NULL,
    device_name VARCHAR(255),
    device_type VARCHAR(50), -- 'desktop', 'mobile', 'tablet'
    browser VARCHAR(100),
    os VARCHAR(100),
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    trusted BOOLEAN DEFAULT FALSE,
    trusted_at TIMESTAMP,
    ip_addresses JSONB, -- Array of IPs this device has used
    is_active BOOLEAN DEFAULT TRUE
);

-- Security alerts table
CREATE TABLE IF NOT EXISTS security_alerts (
    id SERIAL PRIMARY KEY,
    alert_type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL, -- 'low', 'medium', 'high', 'critical'
    user_id INTEGER,
    admin_id INTEGER,
    ip VARCHAR(45),
    triggered_by_event_id INTEGER,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by INTEGER,
    acknowledged_at TIMESTAMP,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by INTEGER,
    resolved_at TIMESTAMP,
    resolution_notes TEXT,
    metadata JSONB
);

-- Enhanced password history table (supports both users and admins)
CREATE TABLE IF NOT EXISTS password_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    table_name VARCHAR(20) NOT NULL, -- 'users' or 'admins'
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Note: No foreign key constraint to support both users and admins tables
    CONSTRAINT chk_table_name CHECK (table_name IN ('users', 'admins'))
);

-- Security settings table
CREATE TABLE IF NOT EXISTS security_settings (
    id INTEGER PRIMARY KEY DEFAULT 1,
    max_login_attempts INTEGER DEFAULT 5,
    lockout_duration INTEGER DEFAULT 30,
    password_expiry_days INTEGER DEFAULT 90,
    require_strong_passwords BOOLEAN DEFAULT TRUE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    min_password_length INTEGER DEFAULT 8,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_reset_token ON users(reset_password_token);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login);

CREATE INDEX IF NOT EXISTS idx_admins_email ON admins(email);
CREATE INDEX IF NOT EXISTS idx_admins_role ON admins(role);
CREATE INDEX IF NOT EXISTS idx_admins_is_main ON admins(is_main_admin);
CREATE INDEX IF NOT EXISTS idx_admins_two_factor ON admins(two_factor_enabled);
CREATE INDEX IF NOT EXISTS idx_admins_reset_token ON admins(reset_password_token);
CREATE INDEX IF NOT EXISTS idx_admins_last_login ON admins(last_login);

CREATE INDEX IF NOT EXISTS idx_scholarships_title ON scholarships(title);
CREATE INDEX IF NOT EXISTS idx_scholarships_deadline ON scholarships(deadline);
CREATE INDEX IF NOT EXISTS idx_scholarships_is_open ON scholarships(is_open);
CREATE INDEX IF NOT EXISTS idx_scholarships_level ON scholarships(level);
CREATE INDEX IF NOT EXISTS idx_scholarships_country ON scholarships(country);
CREATE INDEX IF NOT EXISTS idx_scholarships_created_by ON scholarships(created_by);

CREATE INDEX IF NOT EXISTS idx_messages_email ON messages(email);
CREATE INDEX IF NOT EXISTS idx_messages_status ON messages(status);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);

CREATE INDEX IF NOT EXISTS idx_newsletter_email ON newsletter_subscriptions(email);

-- Enhanced security events indexes (only create if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'security_events') THEN
    CREATE INDEX IF NOT EXISTS idx_security_events_type ON security_events(event_type);
    CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
    CREATE INDEX IF NOT EXISTS idx_security_events_admin_id ON security_events(admin_id);
    CREATE INDEX IF NOT EXISTS idx_security_events_email ON security_events(email);
    CREATE INDEX IF NOT EXISTS idx_security_events_ip ON security_events(ip);
    CREATE INDEX IF NOT EXISTS idx_security_events_timestamp ON security_events(timestamp);
    CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
    CREATE INDEX IF NOT EXISTS idx_security_events_resolved ON security_events(resolved);
    CREATE INDEX IF NOT EXISTS idx_security_events_risk_score ON security_events(risk_score);
    CREATE INDEX IF NOT EXISTS idx_security_events_session_id ON security_events(session_id);
  END IF;
END $$;

-- Login attempts indexes (only create if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'login_attempts') THEN
    CREATE INDEX IF NOT EXISTS idx_login_attempts_email ON login_attempts(email);
    CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip);
    CREATE INDEX IF NOT EXISTS idx_login_attempts_timestamp ON login_attempts(timestamp);
    CREATE INDEX IF NOT EXISTS idx_login_attempts_success ON login_attempts(success);
    CREATE INDEX IF NOT EXISTS idx_login_attempts_email_ip ON login_attempts(email, ip);
  END IF;
END $$;

-- Trusted devices indexes (only create if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'trusted_devices') THEN
    CREATE INDEX IF NOT EXISTS idx_trusted_devices_user_id ON trusted_devices(user_id);
    CREATE INDEX IF NOT EXISTS idx_trusted_devices_admin_id ON trusted_devices(admin_id);
    CREATE INDEX IF NOT EXISTS idx_trusted_devices_fingerprint ON trusted_devices(device_fingerprint);
    CREATE INDEX IF NOT EXISTS idx_trusted_devices_trusted ON trusted_devices(trusted);
    CREATE INDEX IF NOT EXISTS idx_trusted_devices_active ON trusted_devices(is_active);
    CREATE INDEX IF NOT EXISTS idx_trusted_devices_last_seen ON trusted_devices(last_seen);
  END IF;
END $$;

-- Security alerts indexes (only create if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'security_alerts') THEN
    CREATE INDEX IF NOT EXISTS idx_security_alerts_type ON security_alerts(alert_type);
    CREATE INDEX IF NOT EXISTS idx_security_alerts_severity ON security_alerts(severity);
    CREATE INDEX IF NOT EXISTS idx_security_alerts_user_id ON security_alerts(user_id);
    CREATE INDEX IF NOT EXISTS idx_security_alerts_admin_id ON security_alerts(admin_id);
    CREATE INDEX IF NOT EXISTS idx_security_alerts_timestamp ON security_alerts(timestamp);
    CREATE INDEX IF NOT EXISTS idx_security_alerts_acknowledged ON security_alerts(acknowledged);
    CREATE INDEX IF NOT EXISTS idx_security_alerts_resolved ON security_alerts(resolved);
  END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON password_history(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns (drop existing first to avoid conflicts)
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_admins_updated_at ON admins;
DROP TRIGGER IF EXISTS update_scholarships_updated_at ON scholarships;
DROP TRIGGER IF EXISTS update_messages_updated_at ON messages;
DROP TRIGGER IF EXISTS update_security_settings_updated_at ON security_settings;

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_admins_updated_at BEFORE UPDATE ON admins FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_scholarships_updated_at BEFORE UPDATE ON scholarships FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_security_settings_updated_at BEFORE UPDATE ON security_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
