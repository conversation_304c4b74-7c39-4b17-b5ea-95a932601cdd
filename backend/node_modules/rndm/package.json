{"name": "rndm", "description": "random string generator", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "twitter": "https://twitter.com/jongleberry"}, "license": "MIT", "devDependencies": {"mocha": "2", "istanbul": "0"}, "repository": "crypto-utils/rndm", "scripts": {"test": "mocha", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["random", "number", "generator", "uid", "id"], "files": ["index.js"]}