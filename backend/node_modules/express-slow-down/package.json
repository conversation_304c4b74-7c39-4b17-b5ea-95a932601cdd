{"name": "express-slow-down", "version": "2.1.0", "description": "Basic IP rate-limiting middleware for Express that slows down responses rather than blocking the user.", "homepage": "https://github.com/express-rate-limit/express-slow-down", "author": {"name": "<PERSON>", "url": "http://nfriedly.com/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/express-rate-limit/express-slow-down.git"}, "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/", "tsconfig.json", "readme.md", "license.md", "changelog.md"], "engines": {"node": ">= 16"}, "scripts": {"clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --packages=external --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = slowDown; module.exports.default = slowDown; module.exports.slowDown = slowDown; \" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --packages=external --format=esm --outfile=dist/index.mjs source/index.ts", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "compile": "run-s clean build:*", "lint:code": "xo", "lint:rest": "prettier --check .", "lint": "run-s lint:*", "format:code": "xo --fix", "format:rest": "prettier --write .", "format": "run-s format:*", "test:lib": "jest test/library/", "test:int": "jest test/integration/", "test": "run-s lint test:*", "pre-commit": "lint-staged", "prepare": "run-s compile && husky install config/husky"}, "peerDependencies": {"express": "4 || 5 || ^5.0.0-beta.1"}, "dependencies": {"express-rate-limit": "7"}, "devDependencies": {"@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2", "@jest/globals": "29.7.0", "@types/express": "4.17.18", "@types/jest": "29.5.5", "@types/supertest": "2.0.12", "body-parser": "1.20.3", "del-cli": "5.1.0", "dts-bundle-generator": "8.0.1", "esbuild": "0.19.3", "express": "4.21.2", "husky": "8.0.3", "jest": "29.7.0", "jest-expect-message": "1.1.3", "lint-staged": "14.0.1", "npm-run-all": "4.1.5", "prettier": "3.0.3", "supertest": "6.3.3", "ts-jest": "29.1.1", "typescript": "5.2.2", "xo": "0.56.0"}, "xo": {"prettier": true, "rules": {"@typescript-eslint/prefer-nullish-coalescing": ["error", {"ignoreConditionalTests": true}], "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"], "unicorn/prefer-string-replace-all": 0}, "overrides": [{"files": "test/**/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-empty-function": 0, "import/no-named-as-default": 0, "unicorn/prefer-event-target": 0, "unicorn/prevent-abbreviations": 0}}]}, "prettier": "@express-rate-limit/prettier", "lint-staged": {"{source,test}/**/*.ts": "xo --fix", "**/*.{json,yaml,md}": "prettier --write"}}