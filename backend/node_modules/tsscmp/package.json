{"name": "tsscmp", "version": "1.0.6", "description": "Timing safe string compare using double HMAC", "main": "lib/index.js", "dependencies": {}, "devDependencies": {}, "scripts": {"test": "node test/unit && node test/benchmark"}, "repository": {"type": "git", "url": "https://github.com/suryagh/tsscmp.git"}, "keywords": ["timing safe string compare", "double hmac string compare", "safe string compare", "hmac"], "author": "suryagh", "publishConfig": {"registry": "https://registry.npmjs.org"}, "engines": {"node": ">=0.6.x"}, "license": "MIT"}