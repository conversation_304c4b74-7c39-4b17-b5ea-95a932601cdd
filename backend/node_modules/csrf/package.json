{"name": "csrf", "description": "primary logic behind csrf tokens", "version": "3.1.0", "author": "<PERSON> <<EMAIL>> (http://jongleberry.com)", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "pillarjs/csrf", "dependencies": {"rndm": "1.2.0", "tsscmp": "1.0.6", "uid-safe": "2.1.5"}, "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "bluebird": "3.5.4", "eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.16.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "6.1.2"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.d.ts", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --trace-deprecation --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --trace-deprecation --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --trace-deprecation --reporter spec --check-leaks test/"}, "keywords": ["csrf", "tokens"]}