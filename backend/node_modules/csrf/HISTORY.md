3.1.0 / 2019-04-09
==================

  * Include a TypeScript definition file
  * deps: tsscmp@1.0.6
    - Use `crypto.timingSafeEqual` when available
  * deps: uid-safe@2.1.5
    - perf: remove only trailing `=`

3.0.6 / 2017-03-14
==================

  * Remove `base64-url` dependency

3.0.5 / 2017-03-07
==================

  * deps: uid-safe@2.1.4
    - Remove `base64-url` dependency

3.0.4 / 2016-11-13
==================

  * deps: base64-url@1.3.3
  * deps: uid-safe@2.1.3
    - deps: base64-url@1.3.3

3.0.3 / 2016-05-26
==================

  * deps: tsscmp@1.0.5

3.0.2 / 2016-05-22
==================

  * Use `tsscmp` module for timing-safe token verification
  * deps: base64-url@1.2.2
  * deps: uid-safe@2.1.1
    - deps: base64-url@1.2.2

3.0.1 / 2016-01-28
==================

  * deps: rndm@1.2.0
  * deps: uid-safe@2.1.0
    - Use `random-bytes` for byte source

3.0.0 / 2015-05-09
==================

  * Remove `tokenize` export
  * Remove `tokenize` option
  * Return a prototype-based object rather than functions
    - This means the resulting functions need to be called as methods
  * Throw when missing secret to `tokens.create()`
  * deps: uid-safe@~2.0.0
    - Use global `Promise` when returning a promise

2.0.7 / 2015-05-03
==================

  * Fix compatibility with `crypto.DEFAULT_ENCODING` global changes

2.0.6 / 2015-02-13
==================

  * deps: base64-url@1.2.1
  * deps: uid-safe@~1.1.0
    - Use `crypto.randomBytes`, if available
    - deps: base64-url@1.2.1

2.0.5 / 2015-01-31
==================

  * deps: base64-url@1.2.0
  * deps: uid-safe@~1.0.3
    - Fix error branch that would throw
    - deps: base64-url@1.2.0

2.0.4 / 2015-01-08
==================

  * deps: uid-safe@~1.0.2
    - Remove dependency on `mz`

2.0.3 / 2014-12-30
==================

  * Slight speed improvement for `verify`
  * deps: base64-url@1.1.0
  * deps: rndm@~1.1.0

2.0.2 / 2014-11-09
==================

  * deps: scmp@1.0.0

2.0.1 / 2014-08-22
==================

  * Rename module to `csrf`

2.0.0 / 2014-06-18
==================

  * Use `uid-safe` module
  * Use `base64-url` module
  * Remove sync `.secret()` -- use `.secretSync()` instead

1.0.4 / 2014-06-11
==================

  * Make sure CSRF tokens are URL safe
